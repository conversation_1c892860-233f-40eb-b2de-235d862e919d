28:~/staging/ebook-api$ docker ps -a | grep ebook-api-s
f408071cc614   ebook-api-staging:latest          "docker-entrypoint.s…"   4 minutes ago    Up 3 minutes               0.0.0.0:5000->5000/tcp, :::5000->5000/tcp                                                                             ebook-api-staging
ubuntu@ip-172-31-23-228:~/staging/ebook-api$ docker logs --tail 100 -ft f408071cc614
2025-07-16T10:09:02.293846373Z yarn run v1.22.5
2025-07-16T10:09:02.328342363Z $ node server.js
2025-07-16T10:09:03.416940035Z HTTP Server running on port 5000
2025-07-16T10:09:03.480920339Z MongoDB connected
tput cup 0 0
ls -al
tput ed
^X^C
ubuntu@ip-172-31-23-228:~/staging/ebook-api$ clear -x
ubuntu@ip-172-31-23-228:~/staging/ebook-api$ docker logs --tail 100 -ft f408071cc614
2025-07-16T10:09:02.293846373Z yarn run v1.22.5
2025-07-16T10:09:02.328342363Z $ node server.js
2025-07-16T10:09:03.416940035Z HTTP Server running on port 5000
2025-07-16T10:09:03.480920339Z MongoDB connected
^C
ubuntu@ip-172-31-23-228:~/staging/ebook-api$ docker logs --tail 100 -ft f408071cc614
2025-07-16T10:09:02.293846373Z yarn run v1.22.5
2025-07-16T10:09:02.328342363Z $ node server.js
2025-07-16T10:09:03.416940035Z HTTP Server running on port 5000
2025-07-16T10:09:03.480920339Z MongoDB connected
^C
ubuntu@ip-172-31-23-228:~/staging/ebook-api$ docker logs --tail 100 -ft f408071cc614
2025-07-16T10:09:02.293846373Z yarn run v1.22.5
2025-07-16T10:09:02.328342363Z $ node server.js
2025-07-16T10:09:03.416940035Z HTTP Server running on port 5000
2025-07-16T10:09:03.480920339Z MongoDB connected
^C
ubuntu@ip-172-31-23-228:~/staging/ebook-api$ docker ps -a | grep ebook-api
f408071cc614   ebook-api-staging:latest          "docker-entrypoint.s…"   12 minutes ago   Up 12 minutes              0.0.0.0:5000->5000/tcp, :::5000->5000/tcp                                                                             ebook-api-staging
ubuntu@ip-172-31-23-228:~/staging/ebook-api$ docker logs --tail 100 -ft f408071cc614
2025-07-16T10:32:00.761901547Z     at next (/home/<USER>/app/node_modules/express/lib/router/index.js:275:10)
2025-07-16T10:32:00.761904521Z     at next (/home/<USER>/app/node_modules/express/lib/router/route.js:127:14)
2025-07-16T10:32:00.761918945Z     at /home/<USER>/app/Routes/User.js:15:35
2025-07-16T10:32:00.761922418Z     at Layer.handle [as handle_request] (/home/<USER>/app/node_modules/express/lib/router/layer.js:95:5)
2025-07-16T10:32:00.761924872Z     at next (/home/<USER>/app/node_modules/express/lib/router/route.js:137:13)
2025-07-16T10:32:00.761927234Z     at Route.dispatch (/home/<USER>/app/node_modules/express/lib/router/route.js:112:3)
2025-07-16T10:32:00.761929646Z     at Layer.handle [as handle_request] (/home/<USER>/app/node_modules/express/lib/router/layer.js:95:5)
2025-07-16T10:32:00.761932160Z     at /home/<USER>/app/node_modules/express/lib/router/index.js:281:22
2025-07-16T10:32:00.761934556Z     at param (/home/<USER>/app/node_modules/express/lib/router/index.js:354:14)
2025-07-16T10:32:00.761937088Z (node:29) UnhandledPromiseRejectionWarning: Unhandled promise rejection. This error originated either by throwing inside of an async function without a catch block, or by rejecting a promise which was not handled with .catch(). To terminate the node process on unhandled promise rejection, use the CLI flag `--unhandled-rejections=strict` (see https://nodejs.org/api/cli.html#cli_unhandled_rejections_mode). (rejection id: 3)
2025-07-16T10:32:00.767317029Z (node:29) UnhandledPromiseRejectionWarning: ReferenceError: https is not defined
2025-07-16T10:32:00.767330654Z     at /home/<USER>/app/Routes/User.js:397:20
2025-07-16T10:32:00.767333641Z     at Layer.handle [as handle_request] (/home/<USER>/app/node_modules/express/lib/router/layer.js:95:5)
2025-07-16T10:32:00.767336217Z     at next (/home/<USER>/app/node_modules/express/lib/router/route.js:137:13)
2025-07-16T10:32:00.767338594Z     at Route.dispatch (/home/<USER>/app/node_modules/express/lib/router/route.js:112:3)
2025-07-16T10:32:00.767341078Z     at Layer.handle [as handle_request] (/home/<USER>/app/node_modules/express/lib/router/layer.js:95:5)
2025-07-16T10:32:00.767343478Z     at /home/<USER>/app/node_modules/express/lib/router/index.js:281:22
2025-07-16T10:32:00.767345833Z     at Function.process_params (/home/<USER>/app/node_modules/express/lib/router/index.js:335:12)
2025-07-16T10:32:00.767349376Z     at next (/home/<USER>/app/node_modules/express/lib/router/index.js:275:10)
2025-07-16T10:32:00.767351849Z     at next (/home/<USER>/app/node_modules/express/lib/router/route.js:127:14)
2025-07-16T10:32:00.767354300Z     at /home/<USER>/app/Routes/User.js:15:35
2025-07-16T10:32:00.767356786Z     at Layer.handle [as handle_request] (/home/<USER>/app/node_modules/express/lib/router/layer.js:95:5)
2025-07-16T10:32:00.767359218Z     at next (/home/<USER>/app/node_modules/express/lib/router/route.js:137:13)
2025-07-16T10:32:00.767361654Z     at Route.dispatch (/home/<USER>/app/node_modules/express/lib/router/route.js:112:3)
2025-07-16T10:32:00.767364062Z     at Layer.handle [as handle_request] (/home/<USER>/app/node_modules/express/lib/router/layer.js:95:5)
2025-07-16T10:32:00.767366456Z     at /home/<USER>/app/node_modules/express/lib/router/index.js:281:22
2025-07-16T10:32:00.767370081Z     at param (/home/<USER>/app/node_modules/express/lib/router/index.js:354:14)
2025-07-16T10:32:00.767380854Z (node:29) UnhandledPromiseRejectionWarning: Unhandled promise rejection. This error originated either by throwing inside of an async function without a catch block, or by rejecting a promise which was not handled with .catch(). To terminate the node process on unhandled promise rejection, use the CLI flag `--unhandled-rejections=strict` (see https://nodejs.org/api/cli.html#cli_unhandled_rejections_mode). (rejection id: 4)
2025-07-16T10:33:40.812011662Z (node:29) UnhandledPromiseRejectionWarning: ReferenceError: https is not defined
2025-07-16T10:33:40.812039253Z     at /home/<USER>/app/Routes/User.js:397:20
2025-07-16T10:33:40.812042849Z     at Layer.handle [as handle_request] (/home/<USER>/app/node_modules/express/lib/router/layer.js:95:5)
2025-07-16T10:33:40.812046033Z     at next (/home/<USER>/app/node_modules/express/lib/router/route.js:137:13)
2025-07-16T10:33:40.812049193Z     at Route.dispatch (/home/<USER>/app/node_modules/express/lib/router/route.js:112:3)
2025-07-16T10:33:40.812053236Z     at Layer.handle [as handle_request] (/home/<USER>/app/node_modules/express/lib/router/layer.js:95:5)
2025-07-16T10:33:40.812056211Z     at /home/<USER>/app/node_modules/express/lib/router/index.js:281:22
2025-07-16T10:33:40.812059206Z     at Function.process_params (/home/<USER>/app/node_modules/express/lib/router/index.js:335:12)
2025-07-16T10:33:40.812062179Z     at next (/home/<USER>/app/node_modules/express/lib/router/index.js:275:10)
2025-07-16T10:33:40.812065088Z     at next (/home/<USER>/app/node_modules/express/lib/router/route.js:127:14)
2025-07-16T10:33:40.812068021Z     at /home/<USER>/app/Routes/User.js:15:35
2025-07-16T10:33:40.812071020Z     at Layer.handle [as handle_request] (/home/<USER>/app/node_modules/express/lib/router/layer.js:95:5)
2025-07-16T10:33:40.812074066Z     at next (/home/<USER>/app/node_modules/express/lib/router/route.js:137:13)
2025-07-16T10:33:40.812077946Z     at Route.dispatch (/home/<USER>/app/node_modules/express/lib/router/route.js:112:3)
2025-07-16T10:33:40.812080907Z     at Layer.handle [as handle_request] (/home/<USER>/app/node_modules/express/lib/router/layer.js:95:5)
2025-07-16T10:33:40.812083927Z     at /home/<USER>/app/node_modules/express/lib/router/index.js:281:22
2025-07-16T10:33:40.812086853Z     at param (/home/<USER>/app/node_modules/express/lib/router/index.js:354:14)
2025-07-16T10:33:40.812089904Z (node:29) UnhandledPromiseRejectionWarning: Unhandled promise rejection. This error originated either by throwing inside of an async function without a catch block, or by rejecting a promise which was not handled with .catch(). To terminate the node process on unhandled promise rejection, use the CLI flag `--unhandled-rejections=strict` (see https://nodejs.org/api/cli.html#cli_unhandled_rejections_mode). (rejection id: 5)
2025-07-16T10:33:40.812093543Z (node:29) UnhandledPromiseRejectionWarning: ReferenceError: https is not defined
2025-07-16T10:33:40.812096558Z     at /home/<USER>/app/Routes/User.js:397:20
2025-07-16T10:33:40.812099473Z     at Layer.handle [as handle_request] (/home/<USER>/app/node_modules/express/lib/router/layer.js:95:5)
2025-07-16T10:33:40.812103849Z     at next (/home/<USER>/app/node_modules/express/lib/router/route.js:137:13)
2025-07-16T10:33:40.812117239Z     at Route.dispatch (/home/<USER>/app/node_modules/express/lib/router/route.js:112:3)
2025-07-16T10:33:40.812119955Z     at Layer.handle [as handle_request] (/home/<USER>/app/node_modules/express/lib/router/layer.js:95:5)
2025-07-16T10:33:40.812122385Z     at /home/<USER>/app/node_modules/express/lib/router/index.js:281:22
2025-07-16T10:33:40.812124811Z     at Function.process_params (/home/<USER>/app/node_modules/express/lib/router/index.js:335:12)
2025-07-16T10:33:40.812127276Z     at next (/home/<USER>/app/node_modules/express/lib/router/index.js:275:10)
2025-07-16T10:33:40.812129703Z     at next (/home/<USER>/app/node_modules/express/lib/router/route.js:127:14)
2025-07-16T10:33:40.812133743Z     at /home/<USER>/app/Routes/User.js:15:35
2025-07-16T10:33:40.812139391Z     at Layer.handle [as handle_request] (/home/<USER>/app/node_modules/express/lib/router/layer.js:95:5)
2025-07-16T10:33:40.812141980Z     at next (/home/<USER>/app/node_modules/express/lib/router/route.js:137:13)
2025-07-16T10:33:40.812144348Z     at Route.dispatch (/home/<USER>/app/node_modules/express/lib/router/route.js:112:3)
2025-07-16T10:33:40.812418501Z     at Layer.handle [as handle_request] (/home/<USER>/app/node_modules/express/lib/router/layer.js:95:5)
2025-07-16T10:33:40.812420922Z     at /home/<USER>/app/node_modules/express/lib/router/index.js:281:22
2025-07-16T10:33:40.812423264Z     at param (/home/<USER>/app/node_modules/express/lib/router/index.js:354:14)
2025-07-16T10:33:40.812425774Z (node:29) UnhandledPromiseRejectionWarning: Unhandled promise rejection. This error originated either by throwing inside of an async function without a catch block, or by rejecting a promise which was not handled with .catch(). To terminate the node process on unhandled promise rejection, use the CLI flag `--unhandled-rejections=strict` (see https://nodejs.org/api/cli.html#cli_unhandled_rejections_mode). (rejection id: 6)
2025-07-16T10:35:21.009270195Z (node:29) UnhandledPromiseRejectionWarning: ReferenceError: https is not defined
2025-07-16T10:35:21.009309685Z     at /home/<USER>/app/Routes/User.js:397:20
2025-07-16T10:35:21.009314357Z     at Layer.handle [as handle_request] (/home/<USER>/app/node_modules/express/lib/router/layer.js:95:5)
2025-07-16T10:35:21.009317904Z     at next (/home/<USER>/app/node_modules/express/lib/router/route.js:137:13)
2025-07-16T10:35:21.009321240Z     at Route.dispatch (/home/<USER>/app/node_modules/express/lib/router/route.js:112:3)
2025-07-16T10:35:21.009324708Z     at Layer.handle [as handle_request] (/home/<USER>/app/node_modules/express/lib/router/layer.js:95:5)
2025-07-16T10:35:21.009328141Z     at /home/<USER>/app/node_modules/express/lib/router/index.js:281:22
2025-07-16T10:35:21.009331363Z     at Function.process_params (/home/<USER>/app/node_modules/express/lib/router/index.js:335:12)
2025-07-16T10:35:21.009335745Z     at next (/home/<USER>/app/node_modules/express/lib/router/index.js:275:10)
2025-07-16T10:35:21.009339147Z     at next (/home/<USER>/app/node_modules/express/lib/router/route.js:127:14)
2025-07-16T10:35:21.009350195Z     at /home/<USER>/app/Routes/User.js:15:35
2025-07-16T10:35:21.009353039Z     at Layer.handle [as handle_request] (/home/<USER>/app/node_modules/express/lib/router/layer.js:95:5)
2025-07-16T10:35:21.009368390Z     at next (/home/<USER>/app/node_modules/express/lib/router/route.js:137:13)
2025-07-16T10:35:21.009371487Z     at Route.dispatch (/home/<USER>/app/node_modules/express/lib/router/route.js:112:3)
2025-07-16T10:35:21.009374854Z     at Layer.handle [as handle_request] (/home/<USER>/app/node_modules/express/lib/router/layer.js:95:5)
2025-07-16T10:35:21.009378960Z     at /home/<USER>/app/node_modules/express/lib/router/index.js:281:22
2025-07-16T10:35:21.009384696Z     at param (/home/<USER>/app/node_modules/express/lib/router/index.js:354:14)
2025-07-16T10:35:21.009387744Z (node:29) UnhandledPromiseRejectionWarning: Unhandled promise rejection. This error originated either by throwing inside of an async function without a catch block, or by rejecting a promise which was not handled with .catch(). To terminate the node process on unhandled promise rejection, use the CLI flag `--unhandled-rejections=strict` (see https://nodejs.org/api/cli.html#cli_unhandled_rejections_mode). (rejection id: 7)
2025-07-16T10:35:21.118538276Z (node:29) UnhandledPromiseRejectionWarning: ReferenceError: https is not defined
2025-07-16T10:35:21.118568306Z     at /home/<USER>/app/Routes/User.js:397:20
2025-07-16T10:35:21.118572467Z     at Layer.handle [as handle_request] (/home/<USER>/app/node_modules/express/lib/router/layer.js:95:5)
2025-07-16T10:35:21.118575859Z     at next (/home/<USER>/app/node_modules/express/lib/router/route.js:137:13)
2025-07-16T10:35:21.118578748Z     at Route.dispatch (/home/<USER>/app/node_modules/express/lib/router/route.js:112:3)
2025-07-16T10:35:21.118582891Z     at Layer.handle [as handle_request] (/home/<USER>/app/node_modules/express/lib/router/layer.js:95:5)
2025-07-16T10:35:21.118585776Z     at /home/<USER>/app/node_modules/express/lib/router/index.js:281:22
2025-07-16T10:35:21.118588660Z     at Function.process_params (/home/<USER>/app/node_modules/express/lib/router/index.js:335:12)
2025-07-16T10:35:21.118591630Z     at next (/home/<USER>/app/node_modules/express/lib/router/index.js:275:10)
2025-07-16T10:35:21.118594514Z     at next (/home/<USER>/app/node_modules/express/lib/router/route.js:127:14)
2025-07-16T10:35:21.118597859Z     at /home/<USER>/app/Routes/User.js:15:35
2025-07-16T10:35:21.118600832Z     at Layer.handle [as handle_request] (/home/<USER>/app/node_modules/express/lib/router/layer.js:95:5)
2025-07-16T10:35:21.118603655Z     at next (/home/<USER>/app/node_modules/express/lib/router/route.js:137:13)
2025-07-16T10:35:21.118607409Z     at Route.dispatch (/home/<USER>/app/node_modules/express/lib/router/route.js:112:3)
2025-07-16T10:35:21.118610138Z     at Layer.handle [as handle_request] (/home/<USER>/app/node_modules/express/lib/router/layer.js:95:5)
2025-07-16T10:35:21.118612866Z     at /home/<USER>/app/node_modules/express/lib/router/index.js:281:22
2025-07-16T10:35:21.118615705Z     at param (/home/<USER>/app/node_modules/express/lib/router/index.js:354:14)
2025-07-16T10:35:21.118627759Z (node:29) UnhandledPromiseRejectionWarning: Unhandled promise rejection. This error originated either by throwing inside of an async function without a catch block, or by rejecting a promise which was not handled with .catch(). To terminate the node process on unhandled promise rejection, use the CLI flag `--unhandled-rejections=strict` (see https://nodejs.org/api/cli.html#cli_unhandled_rejections_mode). (rejection id: 8)
2025-07-16T10:37:01.034648110Z (node:29) UnhandledPromiseRejectionWarning: ReferenceError: https is not defined
2025-07-16T10:37:01.034683964Z     at /home/<USER>/app/Routes/User.js:397:20
2025-07-16T10:37:01.034689926Z     at Layer.handle [as handle_request] (/home/<USER>/app/node_modules/express/lib/router/layer.js:95:5)
2025-07-16T10:37:01.034694793Z     at next (/home/<USER>/app/node_modules/express/lib/router/route.js:137:13)
2025-07-16T10:37:01.034698523Z     at Route.dispatch (/home/<USER>/app/node_modules/express/lib/router/route.js:112:3)
2025-07-16T10:37:01.034702179Z     at Layer.handle [as handle_request] (/home/<USER>/app/node_modules/express/lib/router/layer.js:95:5)
2025-07-16T10:37:01.034705761Z     at /home/<USER>/app/node_modules/express/lib/router/index.js:281:22
2025-07-16T10:37:01.034709201Z     at Function.process_params (/home/<USER>/app/node_modules/express/lib/router/index.js:335:12)
2025-07-16T10:37:01.034712693Z     at next (/home/<USER>/app/node_modules/express/lib/router/index.js:275:10)
2025-07-16T10:37:01.034716184Z     at next (/home/<USER>/app/node_modules/express/lib/router/route.js:127:14)
2025-07-16T10:37:01.034720772Z     at /home/<USER>/app/Routes/User.js:15:35
2025-07-16T10:37:01.034724317Z     at Layer.handle [as handle_request] (/home/<USER>/app/node_modules/express/lib/router/layer.js:95:5)
2025-07-16T10:37:01.034727954Z     at next (/home/<USER>/app/node_modules/express/lib/router/route.js:137:13)
2025-07-16T10:37:01.034731485Z     at Route.dispatch (/home/<USER>/app/node_modules/express/lib/router/route.js:112:3)
2025-07-16T10:37:01.034735412Z     at Layer.handle [as handle_request] (/home/<USER>/app/node_modules/express/lib/router/layer.js:95:5)
2025-07-16T10:37:01.034740255Z     at /home/<USER>/app/node_modules/express/lib/router/index.js:281:22
2025-07-16T10:37:01.034743717Z     at param (/home/<USER>/app/node_modules/express/lib/router/index.js:354:14)
2025-07-16T10:37:01.034747323Z (node:29) UnhandledPromiseRejectionWarning: Unhandled promise rejection. This error originated either by throwing inside of an async function without a catch block, or by rejecting a promise which was not handled with .catch(). To terminate the node process on unhandled promise rejection, use the CLI flag `--unhandled-rejections=strict` (see https://nodejs.org/api/cli.html#cli_unhandled_rejections_mode). (rejection id: 9)
2025-07-16T10:37:01.230831550Z (node:29) UnhandledPromiseRejectionWarning: ReferenceError: https is not defined
2025-07-16T10:37:01.230867328Z     at /home/<USER>/app/Routes/User.js:397:20
2025-07-16T10:37:01.230872235Z     at Layer.handle [as handle_request] (/home/<USER>/app/node_modules/express/lib/router/layer.js:95:5)
2025-07-16T10:37:01.230876100Z     at next (/home/<USER>/app/node_modules/express/lib/router/route.js:137:13)
2025-07-16T10:37:01.230899891Z     at Route.dispatch (/home/<USER>/app/node_modules/express/lib/router/route.js:112:3)
2025-07-16T10:37:01.230903664Z     at Layer.handle [as handle_request] (/home/<USER>/app/node_modules/express/lib/router/layer.js:95:5)
2025-07-16T10:37:01.230906789Z     at /home/<USER>/app/node_modules/express/lib/router/index.js:281:22
2025-07-16T10:37:01.230910787Z     at Function.process_params (/home/<USER>/app/node_modules/express/lib/router/index.js:335:12)
2025-07-16T10:37:01.230913963Z     at next (/home/<USER>/app/node_modules/express/lib/router/index.js:275:10)
2025-07-16T10:37:01.230917161Z     at next (/home/<USER>/app/node_modules/express/lib/router/route.js:127:14)
2025-07-16T10:37:01.230920173Z     at /home/<USER>/app/Routes/User.js:15:35
2025-07-16T10:37:01.230923072Z     at Layer.handle [as handle_request] (/home/<USER>/app/node_modules/express/lib/router/layer.js:95:5)
2025-07-16T10:37:01.230926110Z     at next (/home/<USER>/app/node_modules/express/lib/router/route.js:137:13)
2025-07-16T10:37:01.230929029Z     at Route.dispatch (/home/<USER>/app/node_modules/express/lib/router/route.js:112:3)
2025-07-16T10:37:01.230932017Z     at Layer.handle [as handle_request] (/home/<USER>/app/node_modules/express/lib/router/layer.js:95:5)
2025-07-16T10:37:01.230935918Z     at /home/<USER>/app/node_modules/express/lib/router/index.js:281:22
2025-07-16T10:37:01.230938850Z     at param (/home/<USER>/app/node_modules/express/lib/router/index.js:354:14)
2025-07-16T10:37:01.230941869Z (node:29) UnhandledPromiseRejectionWarning: Unhandled promise rejection. This error originated either by throwing inside of an async function without a catch block, or by rejecting a promise which was not handled with .catch(). To terminate the node process on unhandled promise rejection, use the CLI flag `--unhandled-rejections=strict` (see https://nodejs.org/api/cli.html#cli_unhandled_rejections_mode). (rejection id: 10)
2025-07-16T10:37:11.346241162Z (node:29) UnhandledPromiseRejectionWarning: ReferenceError: https is not defined
2025-07-16T10:37:11.346281873Z     at /home/<USER>/app/Routes/User.js:397:20
2025-07-16T10:37:11.346286833Z     at Layer.handle [as handle_request] (/home/<USER>/app/node_modules/express/lib/router/layer.js:95:5)
2025-07-16T10:37:11.346290807Z     at next (/home/<USER>/app/node_modules/express/lib/router/route.js:137:13)
2025-07-16T10:37:11.346299271Z     at Route.dispatch (/home/<USER>/app/node_modules/express/lib/router/route.js:112:3)
2025-07-16T10:37:11.346302951Z     at Layer.handle [as handle_request] (/home/<USER>/app/node_modules/express/lib/router/layer.js:95:5)
2025-07-16T10:37:11.346306554Z     at /home/<USER>/app/node_modules/express/lib/router/index.js:281:22
2025-07-16T10:37:11.346310018Z     at Function.process_params (/home/<USER>/app/node_modules/express/lib/router/index.js:335:12)
2025-07-16T10:37:11.346313604Z     at next (/home/<USER>/app/node_modules/express/lib/router/index.js:275:10)
2025-07-16T10:37:11.346317142Z     at next (/home/<USER>/app/node_modules/express/lib/router/route.js:127:14)
2025-07-16T10:37:11.346320812Z     at /home/<USER>/app/Routes/User.js:15:35
2025-07-16T10:37:11.346332128Z     at Layer.handle [as handle_request] (/home/<USER>/app/node_modules/express/lib/router/layer.js:95:5)
2025-07-16T10:37:11.346336311Z     at next (/home/<USER>/app/node_modules/express/lib/router/route.js:137:13)
2025-07-16T10:37:11.346339532Z     at Route.dispatch (/home/<USER>/app/node_modules/express/lib/router/route.js:112:3)
2025-07-16T10:37:11.346342612Z     at Layer.handle [as handle_request] (/home/<USER>/app/node_modules/express/lib/router/layer.js:95:5)
2025-07-16T10:37:11.346345657Z     at /home/<USER>/app/node_modules/express/lib/router/index.js:281:22
2025-07-16T10:37:11.346348696Z     at param (/home/<USER>/app/node_modules/express/lib/router/index.js:354:14)
2025-07-16T10:37:11.346351779Z (node:29) UnhandledPromiseRejectionWarning: Unhandled promise rejection. This error originated either by throwing inside of an async function without a catch block, or by rejecting a promise which was not handled with .catch(). To terminate the node process on unhandled promise rejection, use the CLI flag `--unhandled-rejections=strict` (see https://nodejs.org/api/cli.html#cli_unhandled_rejections_mode). (rejection id: 11)
