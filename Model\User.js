const mongoose = require("mongoose");
const Schema = mongoose.Schema;

const User = new Schema({
	email: {
		required: false,
		type: String,
	},
	phone_number: {
		required: false,
		type: String,
	},
	name: {
		required: false,
		type: String,
	},
	password: {
		required: false,
		type: String,
	},
	is_active: {
		required: true,
		type: Boolean,
	},
	patronid : {
		required : true,
		type: String,
	},
	login_date : {
		required : true,
		type: Date,
	},
	language:{
		required : false,
		type: String,
		default:'en'
	},
	offset : {
		required : false,
		type: String,
		default:'+00'
	},
});

module.exports = mongoose.model("User", User);
