<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;a960c370-db46-4b64-9144-33fd3ce78cda&quot;,&quot;conversations&quot;:{&quot;202a24fd-16aa-4371-9bdb-94eab4c1be33&quot;:{&quot;id&quot;:&quot;202a24fd-16aa-4371-9bdb-94eab4c1be33&quot;,&quot;createdAtIso&quot;:&quot;2025-07-21T09:41:08.566Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-07-21T10:53:19.953Z&quot;,&quot;chatHistory&quot;:[{&quot;request_id&quot;:&quot;3298a2f5-c785-4de4-8ad1-bdc3190a9600&quot;,&quot;uuid&quot;:&quot;da48b3a0-0381-4abe-87c4-cf5ed6f13378&quot;,&quot;chatItemType&quot;:&quot;agentic-checkpoint-delimiter&quot;,&quot;status&quot;:&quot;success&quot;,&quot;fromTimestamp&quot;:0,&quot;toTimestamp&quot;:1753090868569,&quot;seen_state&quot;:&quot;seen&quot;},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;c38b966e-a501-49fe-b33a-5720daf8c368&quot;,&quot;request_message&quot;:&quot;看项目的 Model，我看到 mongoose.shema class 后，可以 new Modelx，多数时候是 Modelx.find()  .findOne()，可以理解为包装拥有了 crud 的 静态方法么&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;看项目的 Model，我看到 mongoose.shema class 后，可以 new Modelx，多数时候是 Modelx.find()  .findOne()，可以理解为包装拥有了 crud 的 静态方法么&quot;}]}]},&quot;mentioned_items&quot;:[],&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_013uJxtPuEcR1EiHfVqAPsrE&quot;,&quot;tool_name&quot;:&quot;codebase-retrieval&quot;,&quot;input_json&quot;:&quot;{\&quot;information_request\&quot;: \&quot;Show me examples of Mongoose schema definitions and model usage in the project, including how models are created from schemas and how CRUD operations like find(), findOne() are used\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:2,&quot;type&quot;:0,&quot;content&quot;:&quot;我来帮你理解 Mongoose 的 Schema 和 Model 的关系。让我先查看一下项目中的 Model 实现来给你更准确的解释。\n&quot;},{&quot;id&quot;:3,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:4,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;text_node&quot;:{&quot;content&quot;:&quot;看项目的 Model，我看到 mongoose.shema class 后，可以 new Modelx，多数时候是 Modelx.find()  .findOne()，可以理解为包装拥有了 crud 的 静态方法么&quot;}},{&quot;id&quot;:1,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;E:/HXL_Book/jrc-service&quot;,&quot;folderRoot&quot;:&quot;E:/HXL_Book/jrc-service&quot;}],&quot;workspaceFoldersUnchanged&quot;:false,&quot;currentTerminal&quot;:{&quot;terminalId&quot;:2,&quot;currentWorkingDirectory&quot;:&quot;E:/HXL_Book/jrc-service&quot;}}}],&quot;timestamp&quot;:&quot;2025-07-21T09:42:22.650Z&quot;,&quot;response_text&quot;:&quot;我来帮你理解 Mongoose 的 Schema 和 Model 的关系。让我先查看一下项目中的 Model 实现来给你更准确的解释。\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;90da819d-c247-47c8-89e4-eb1fff78057a&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_016KqiWujfThyiePoLCJttzf&quot;,&quot;tool_name&quot;:&quot;codebase-retrieval&quot;,&quot;input_json&quot;:&quot;{\&quot;information_request\&quot;: \&quot;Show me more examples of how Mongoose models are used for CRUD operations like save(), create(), findOneAndUpdate(), deleteOne(), etc. in the project routes and helpers\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:2,&quot;type&quot;:0,&quot;content&quot;:&quot;现在让我再查看一些具体的 CRUD 操作使用示例：\n&quot;},{&quot;id&quot;:3,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:4,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;The following code sections were retrieved:\nPath: Model/User.js\n     1\tconst mongoose = require(\&quot;mongoose\&quot;);\n     2\tconst Schema = mongoose.Schema;\n     3\t\n     4\tconst User = new Schema({\n     5\t\temail: {\n     6\t\t\trequired: false,\n     7\t\t\ttype: String,\n     8\t\t},\n     9\t\tphone_number: {\n    10\t\t\trequired: false,\n    11\t\t\ttype: String,\n    12\t\t},\n    13\t\tname: {\n    14\t\t\trequired: false,\n    15\t\t\ttype: String,\n    16\t\t},\n    17\t\tpassword: {\n    18\t\t\trequired: false,\n    19\t\t\ttype: String,\n    20\t\t},\n    21\t\tis_active: {\n    22\t\t\trequired: true,\n    23\t\t\ttype: Boolean,\n    24\t\t},\n    25\t\tpatronid : {\n    26\t\t\trequired : true,\n    27\t\t\ttype: String,\n    28\t\t},\n    29\t\tlogin_date : {\n    30\t\t\trequired : true,\n    31\t\t\ttype: Date,\n    32\t\t},\n    33\t\tlanguage:{\n    34\t\t\trequired : false,\n    35\t\t\ttype: String,\n    36\t\t\tdefault:'en'\n    37\t\t},\n    38\t\toffset : {\n    39\t\t\trequired : false,\n    40\t\t\ttype: String,\n    41\t\t\tdefault:'+00'\n    42\t\t},\n    43\t});\n    44\t\n    45\tmodule.exports = mongoose.model(\&quot;User\&quot;, User);\n...\nPath: Model/Admin.js\n     1\tconst mongoose = require(\&quot;mongoose\&quot;);\n     2\tconst Schema = mongoose.Schema;\n     3\tconst Admin = new Schema({\n     4\t\tname: {\n     5\t\t\ttype: String,\n     6\t\t\trequired: true,\n     7\t\t},\n     8\t\temail: {\n     9\t\t\ttype: String,\n    10\t\t\trequired: true,\n    11\t\t},\n    12\t\tphone_number: {\n    13\t\t\ttype: String,\n    14\t\t\trequired: true,\n    15\t\t},\n    16\t\tpassword: {\n    17\t\t\ttype: String,\n    18\t\t\trequired: true,\n    19\t\t},\n    20\t\tis_active: {\n    21\t\t\ttype: Boolean,\n    22\t\t\trequired: true,\n    23\t\t},\n    24\t\tsuper_admin: {\n    25\t\t\ttype: Boolean,\n    26\t\t\tdefault: false,\n    27\t\t},\n    28\t\treset_password_token: {\n    29\t\t\ttype: String\n    30\t\t},\n    31\t\treset_password_expires: {\n    32\t\t\ttype: String\n    33\t\t},\n    34\t\trole: {\n    35\t\t\ttype: String,\n    36\t\t\trequired: true,\n    37\t\t\tdefault: \&quot;2\&quot;,\n    38\t\t},\n    39\t\tcollection_type: {\n    40\t\t\ttype: String,\n    41\t\t\trequired: false,\n    42\t\t\tdefault:'JYG'\n    43\t\t},\n    44\t});\n    45\tmodule.exports = mongoose.model(\&quot;Admin\&quot;, Admin);...\nPath: Model/Book.js\n...\n     3\t\n     4\tconst Book = new Schema({\n     5\t\tcategory_id: [\n     6\t\t\t{\n     7\t\t\t\ttype: Schema.Types.ObjectId,\n     8\t\t\t\tref: \&quot;Category\&quot;,\n     9\t\t\t},\n    10\t\t],\n    11\t\ttitle: {\n    12\t\t\trequired: true,\n    13\t\t\ttype: String,\n    14\t\t},\n    15\t\texcerpt: {\n    16\t\t\trequired: true,\n    17\t\t\ttype: String,\n    18\t\t},\n    19\t\tstock_quantity: {\n    20\t\t\trequired: true,\n    21\t\t\ttype: Number,\n    22\t\t},\n    23\t\tavailable_quantity: {\n    24\t\t\ttype: Number,\n    25\t\t},\n    26\t\tpublishingGroup: {\n    27\t\t\ttype: String,\n    28\t\t},\n    29\t\timprints: {\n    30\t\t\ttype: String,\n    31\t\t},\n    32\t\tauthor: {\n    33\t\t\trequired: true,\n    34\t\t\ttype: String,\n    35\t\t},\n    36\t\ttotal_pages: {\n    37\t\t\trequired: false,\n    38\t\t\ttype: Number,\n    39\t\t},\n    40\t\tcost: {\n    41\t\t\trequired: false,\n    42\t\t\ttype: String,\n    43\t\t},\n    44\t\tcover_photo: {\n    45\t\t\trequired: true,\n    46\t\t\ttype: String,\n    47\t\t},\n    48\t\tbook_pdf: {\n    49\t\t\trequired: true,\n    50\t\t\ttype: String,\n    51\t\t},\n    52\t\tbook_recomm : {\n    53\t\t\trequired: false,\n    54\t\t\ttype:Boolean,\n    55\t\t\tdefault: false,\n    56\t\t},\n    57\t\tbook_recomm_datetime : {\n    58\t\t\ttype: Date,\n    59\t\t\trequired: false\n    60\t\t},\n    61\t\tpreview_book: {\n    62\t\t\trequired: false,\n    63\t\t\ttype: String,\n    64\t\t},\n    65\t\tadded_by: {\n    66\t\t\ttype: Schema.Types.ObjectId,\n    67\t\t\tref: \&quot;Admin\&quot;,\n    68\t\t\trequired: true,\n    69\t\t},\n    70\t\tadded_at: {\n    71\t\t\ttype: Date,\n    72\t\t\trequired: true,\n    73\t\t},\n    74\t\tpublish_date :{\n    75\t\t\ttype: Date,\n    76\t\t\trequired: false,\n    77\t\t},\n    78\t\tisbn_no: {\n    79\t\t\ttype: String,\n    80\t\t\trequired: true\n    81\t\t},\n    82\t\tis_deleted: {\n    83\t\t\trequired: false,\n    84\t\t\ttype:Boolean,\n    85\t\t\tdefault: false,\n    86\t\t},\n    87\t\tdeleted_at: {\n    88\t\t\trequired: false,\n    89\t\t\ttype: Date,\n    90\t\t},\n    91\t    collection_type: {\n    92\t\t\ttype: String,\n    93\t\t\trequired: false,\n    94\t\t\tdefault:'JYG'\n    95\t\t},\n    96\t});\n    97\tmodule.exports = mongoose.model(\&quot;Book\&quot;, Book);...\nPath: Model/Reading.js\n     1\tconst mongoose = require(\&quot;mongoose\&quot;);\n     2\tconst Schema = mongoose.Schema;\n     3\tconst Reading = new Schema({\n     4\t    book_id: {\n     5\t        type: Schema.Types.ObjectId,\n     6\t        ref: \&quot;Book\&quot;,\n     7\t        required: true,\n     8\t    },\n     9\t\tip: {\n    10\t\t\ttype: String,\n    11\t\t\trequired: true,\n    12\t\t},\n    13\t    client_id: {\n    14\t        type: String,\n    15\t\t\trequired: true,   \t\n    16\t    },\n    17\t    read_date: {\n    18\t        type: Date,\n    19\t        required: true,\n    20\t    },\n    21\t    return_date: {\n    22\t        type: Date,\n    23\t        required: true,\n    24\t    },\n    25\t    returned: {\n    26\t        type: Boolean,\n    27\t        default: false,\n    28\t    },\n    29\t});\n    30\tmodule.exports = mongoose.model(\&quot;Reading\&quot;, Reading);...\nPath: Model/Reserve.js\n     1\tconst mongoose = require(\&quot;mongoose\&quot;);\n     2\tconst Schema = mongoose.Schema;\n     3\tconst Reserve = new Schema({\n     4\t\tuser_id: {\n     5\t        type: Schema.Types.ObjectId,\n     6\t        ref: \&quot;User\&quot;,\n     7\t        required: true,\n     8\t    },\n     9\t    book_id: {\n    10\t        type: Schema.Types.ObjectId,\n    11\t        ref: \&quot;Book\&quot;,\n    12\t        required: true,\n    13\t    },\n    14\t\temail: {\n    15\t\t\ttype: String,\n    16\t\t\trequired: true,\n    17\t\t},\n    18\t\treserve_date: {\n    19\t\t\ttype: Date,\n    20\t\t\trequired: true,\n    21\t\t},\n    22\t\tstatus: {\n    23\t\t\ttype: Boolean,\n    24\t\t\trequired: true,\n    25\t\t},\n    26\t\tis_deleted: {\n    27\t\t\ttype: Boolean,\n    28\t\t\tdefault: false,\n    29\t\t},\n    30\t\tis_mailed:{\n    31\t\t\ttype: Boolean,\n    32\t\t\tdefault: false,\n    33\t\t},\n    34\t\tis_ignored:{\n    35\t\t\ttype: Boolean,\n    36\t\t\tDefault: false,\n    37\t\t},\n    38\t\tmail_date: {\n    39\t\t\ttype: Date,\n    40\t\t\trequired: false,\n    41\t\t},\n    42\t\tis_blocked: {\n    43\t\t\ttype: Boolean,\n    44\t\t\trequired: false,\n    45\t\t\tdefault: false\n    46\t\t}\n    47\t});\n    48\tmodule.exports = mongoose.model(\&quot;Reserve\&quot;, Reserve);...\nPath: Routes/Book.js\n     1\tconst app = require(\&quot;express\&quot;).Router();\n     2\tvar multer = require(\&quot;multer\&quot;);\n     3\tconst Admin = require(\&quot;../Model/Admin\&quot;);\n     4\tconst { AdminHelper } = require(\&quot;../Helper/AdminHelper\&quot;);\n     5\tconst { UserHelper } = require(\&quot;../Helper/UserHelper\&quot;);\n     6\tconst Book = require(\&quot;../Model/Book\&quot;);\n     7\tconst Banner = require(\&quot;../Model/Banner\&quot;);\n     8\tconst Category = require(\&quot;../Model/Category\&quot;);\n     9\tconst fs = require(\&quot;fs\&quot;);\n    10\tconst AdminSession = require(\&quot;../Model/AdminSession\&quot;);\n    11\tconst UserSession = require(\&quot;../Model/UserSession\&quot;);\n    12\tconst Borrow = require(\&quot;../Model/Borrow\&quot;);\n    13\tconst Reading = require(\&quot;../Model/Reading\&quot;);\n    14\tconst Reserve = require(\&quot;../Model/Reserve\&quot;);\n    15\tconst Favourite = require(\&quot;../Model/Favourite\&quot;);\n    16\tconst Preview = require(\&quot;../Model/Preview\&quot;);\n    17\t// const cron = require('node-cron');\n...\n  1594\t\n  1595\t\t\tBook.findOne({\n  1596\t\t\t\t_id: mongoose.Types.ObjectId(req.body.id),\n  1597\t\t\t\t$or: [{ is_deleted: { $exists: false } }, { is_deleted: { $exists: true, $eq: false } }]\n  1598\t\t\t})\n  1599\t\t\t\t.populate(\&quot;category_id\&quot;)\n  1600\t\t\t\t.then(async (resx) =&gt; {\n  1601\t\t\t\t\tif (resx.length === 0) {\n  1602\t\t\t\t\t\tres.json({\n  1603\t\t\t\t\t\t\tcode: 422,\n  1604\t\t\t\t\t\t\tmessage: \&quot;Provided invalid id\&quot;,\n  1605\t\t\t\t\t\t\tstatus: false,\n  1606\t\t\t\t\t\t});\n  1607\t\t\t\t\t\treturn res.end();\n  1608\t\t\t\t\t}\n  1609\t\n  1610\t\t\t\t\tvar category_id = resx.category_id;\n  1611\t\t\t\t\tvar catResp = [];\n  1612\t\t\t\t\tcategory_id.forEach(function (el) {\n  1613\t\t\t\t\t\tvar cat_id = el._id;\n  1614\t\t\t\t\t\tBook.findOne({\n  1615\t\t\t\t\t\t\tcategory_id: {\n  1616\t\t\t\t\t\t\t\t$in: cat_id\n  1617\t\t\t\t\t\t\t},\n  1618\t\t\t\t\t\t\t_id: {\n  1619\t\t\t\t\t\t\t\t$ne: req.body.id\n  1620\t\t\t\t\t\t\t}\n  1621\t\t\t\t\t\t}).then(async (catRes) =&gt; {\n  1622\t\t\t\t\t\t\tif (catRes &amp;&amp; catRes != null) {\n  1623\t\t\t\t\t\t\t\tcatResp.push(catRes);\n  1624\t\t\t\t\t\t\t}\n  1625\t\t\t\t\t\t});\n  1626\t\t\t\t\t});\n  1627\t\n  1628\t\n  1629\t\t\t\t\tres.json({\n  1630\t\t\t\t\t\tcode: 200,\n  1631\t\t\t\t\t\tdata: {\n  1632\t\t\t\t\t\t\tbook: resx,\n  1633\t\t\t\t\t\t\tcatResp: catResp,\n  1634\t\t\t\t\t\t\tborrowed: true\n  1635\t\t\t\t\t\t},\n  1636\t\t\t\t\t\tmessage: \&quot;Operation successful.\&quot;,\n  1637\t\t\t\t\t});\n  1638\t\t\t\t\treturn res.end();\n  1639\t\t\t\t});\n  1640\t\t}\n  1641\t\n  1642\t});\n...\n  2058\t\tif (req.body.category &amp;&amp; req.body.is_available &amp;&amp; req.body.query) {\n  2059\t\t\tif (req.body.queryType == '1') {\n  2060\t\t\t\tlet match = {\n  2061\t\t\t\t\ttitle: {\n  2062\t\t\t\t\t\t$regex: req.body.query,\n  2063\t\t\t\t\t\t$options: \&quot;i\&quot;\n  2064\t\t\t\t\t},\n  2065\t\t\t\t\tcategory_id: mongoose.Types.ObjectId(req.body.category),\n  2066\t\t\t\t\tavailable_quantity: { $gt: 0 }\n  2067\t\t\t\t};\n  2068\t\t\t\tawait filterRecords(res, match, sortBy, sortByType, collection_type);\n  2069\t\t\t}\n  2070\t\t\tif (req.body.queryType == '2') {\n  2071\t\t\t\tlet match = {\n  2072\t\t\t\t\tauthor: {\n  2073\t\t\t\t\t\t$regex: req.body.query,\n  2074\t\t\t\t\t\t$options: \&quot;i\&quot;\n  2075\t\t\t\t\t},\n  2076\t\t\t\t\tcategory_id: mongoose.Types.ObjectId(req.body.category),\n  2077\t\t\t\t\tavailable_quantity: { $gt: 0 }\n  2078\t\t\t\t};\n  2079\t\t\t\tawait filterRecords(res, match, sortBy, sortByType, collection_type);\n  2080\t\t\t}\n...\n  2219\t\n  2220\t\n  2221\t\tif (sortByType) { if (sortByType == \&quot;asc\&quot;) { sortByType = 1; } if (sortByType == \&quot;desc\&quot;) { sortByType = -1; } }\n  2222\t\tvar reslt = await Book.find(_match, { title: 1, author: 1, publish_date: 1, cover_photo: 1 }).sort([[sortbycol, sortByType]]).exec(function (err, result) {\n  2223\t\t\tif (err) {\n  2224\t\t\t\tres.json({\n  2225\t\t\t\t\tcode: 500,\n  2226\t\t\t\t\tmessage: \&quot;Internal error in query book\&quot;,\n  2227\t\t\t\t\tsuccess: false,\n  2228\t\t\t\t});\n  2229\t\t\t\treturn res.end();\n  2230\t\t\t}\n  2231\t\n  2232\t\t\tif (!result) {\n  2233\t\t\t\tres.json({\n  2234\t\t\t\t\tcode: 422,\n  2235\t\t\t\t\tmessage: \&quot;Operation failed\&quot;,\n  2236\t\t\t\t\tsuccess: false,\n  2237\t\t\t\t});\n  2238\t\t\t\treturn res.end();\n  2239\t\t\t}\n  2240\t\n  2241\t\t\tres.json({\n  2242\t\t\t\tcode: 200,\n  2243\t\t\t\tdata: result,\n  2244\t\t\t\tmessage: \&quot;Operation successful.\&quot;\n  2245\t\t\t});\n  2246\t\t\tres.end();\n  2247\t\t});\n...\nPath: Model/Borrow.js\n     1\tconst mongoose = require(\&quot;mongoose\&quot;);\n     2\tconst Schema = mongoose.Schema;\n     3\t\n     4\tconst Borrow = new Schema({\n     5\t    user_id: {\n     6\t        type: Schema.Types.ObjectId,\n     7\t        ref: \&quot;User\&quot;,\n     8\t        required: true,\n     9\t    },\n    10\t    book_id: {\n    11\t        type: Schema.Types.ObjectId,\n    12\t        ref: \&quot;Book\&quot;,\n    13\t        required: true,\n    14\t    },\n    15\t    email: {\n    16\t        type: String,\n    17\t    },\n    18\t    issue_date: {\n    19\t        type: Date,\n    20\t        required: true,\n    21\t    },\n    22\t    return_date: {\n    23\t        type: Date,\n    24\t        required: true,\n    25\t    },\n    26\t    returned: {\n    27\t        type: Boolean,\n    28\t        default: false,\n    29\t    },\n    30\t    reborrowed_once: {\n    31\t        type: Boolean,\n    32\t        default: false\n    33\t    },\n    34\t    reborrow_one_date: {\n    35\t        type: Date\n    36\t    },\n    37\t    reborrowed_twice: {\n    38\t        type: Boolean,\n    39\t        default: false\n    40\t    },\n    41\t    is_deleted: {\n    42\t        type: Boolean,\n    43\t        default: false\n    44\t    },\n    45\t    reborrow_two_date: {\n    46\t        type: Date\n    47\t    }\n    48\t});\n    49\t\n    50\tmodule.exports = mongoose.model(\&quot;Borrow\&quot;, Borrow);\n...\nPath: Model/Banner.js\n     1\tconst mongoose = require(\&quot;mongoose\&quot;);\n     2\tconst Schema = mongoose.Schema;\n     3\t\n     4\tconst Banner = new Schema({\n     5\t    bannerimage: {\n     6\t        required: true,\n     7\t        type: String,\n     8\t    },\n     9\t    url: {\n    10\t        required: true,\n    11\t        type: String,\n    12\t    },\n    13\t    collection_type: {\n    14\t\t\ttype: String,\n    15\t\t\trequired: false,\n    16\t\t\tdefault:'JYG'\n    17\t\t},\n    18\t}, {timestamps: true});\n    19\t\n    20\tmodule.exports = mongoose.model(\&quot;banner\&quot;, Banner);\n...\nPath: Model/UserSession.js\n     1\tconst mongoose = require(\&quot;mongoose\&quot;);\n     2\tconst Schema = mongoose.Schema;\n     3\t\n     4\tconst UserSession = new Schema({\n     5\t\tuser_id: {\n     6\t\t\trequired: true,\n     7\t\t\ttype: Schema.Types.ObjectId,\n     8\t\t\tref: \&quot;User\&quot;,\n     9\t\t},\n    10\t\ttoken: {\n    11\t\t\trequired: true,\n    12\t\t\ttype: String,\n    13\t\t},\n    14\t\tis_active: {\n    15\t\t\trequired: true,\n    16\t\t\ttype: Boolean,\n    17\t\t},\n    18\t\tip: {\n    19\t\t\trequired: false,\n    20\t\t\ttype: String,\n    21\t\t},\n    22\t\tuser_agent: {\n    23\t\t\trequired: true,\n    24\t\t\ttype: String,\n    25\t\t},\n    26\t\tdevice: {\n    27\t\t\trequired: true,\n    28\t\t\ttype: String,\n    29\t\t},\n    30\t\tdate:{\n    31\t\t\trequired : true,\n    32\t\t\ttype: Date,\n    33\t\t},\n    34\t\tforceLogoutAt:{\n    35\t\t\trequired : false,\n    36\t\t\ttype: Date,\n    37\t\t},\n    38\t\tlogoutAt:{\n    39\t\t\trequired : false,\n    40\t\t\ttype: Date,\n    41\t\t},\n    42\t\ttimestamp: { type: Date, default: Date.now}\n    43\t});\n    44\t\n    45\tmodule.exports = mongoose.model(\&quot;UserSession\&quot;, UserSession);...\nPath: Helper/BookHelper.js\n     1\tconst Borrow = require(\&quot;../Model/Borrow\&quot;);\n     2\tconst Favourite = require(\&quot;../Model/Favourite\&quot;);\n     3\tconst moment = require(\&quot;moment\&quot;);\n     4\tconst Book = require(\&quot;../Model/Book\&quot;);\n     5\tconst Preview = require(\&quot;../Model/Preview\&quot;);\n     6\tconst Reserve = require(\&quot;../Model/Reserve\&quot;);\n     7\tconst Admin = require(\&quot;../Model/Admin\&quot;);\n     8\tconst Category = require(\&quot;../Model/Category\&quot;);\n     9\tvar mongoose = require('mongoose');\n    10\tconst async = require(\&quot;async\&quot;);\n    11\tconst R = require('ramda')\n    12\tasync function isBookBorrowed(userId, bookId) {\n    13\t  const borrows = await Borrow.find({\n    14\t    user_id: mongoose.Types.ObjectId(userId),\n    15\t    book_id: mongoose.Types.ObjectId(bookId),\n    16\t    returned: false,\n    17\t  });\n...\n   179\t\n   180\tasync function updateAvailableStock(bookId, type) {\n   181\t  const books = await Book.findOne({\n   182\t    _id: mongoose.Types.ObjectId(bookId)\n   183\t  });\n   184\t\n   185\t  if (!books) {\n   186\t    console.log(`Book not found: ${bookId}`);\n   187\t    return;\n   188\t  }\n   189\t\n   190\t  const originalQuantity = books.available_quantity;\n   191\t  console.log(`updateAvailableStock - Book: ${bookId}, Type: ${type}, Current stock: ${originalQuantity}/${books.stock_quantity}`);\n   192\t\n   193\t  // If available_quantity column is not exist in table then it will update this\n   194\t  if (type == \&quot;borrow\&quot;) {\n   195\t    var available_quantity = parseInt(books.available_quantity) - 1;\n   196\t    await Book.findOneAndUpdate({\n   197\t      _id: mongoose.Types.ObjectId(bookId)\n   198\t    }, {\n   199\t      available_quantity: available_quantity\n   200\t    });\n   201\t    console.log(`Stock decreased for borrow: ${originalQuantity} -&gt; ${available_quantity}`);\n   202\t  }\n...\n   224\t\n   225\tasync function renewBooksbydateV2(startDate, endDate, email, collection_type) {\n   226\t  //  const _startDate = moment(startDate)\n   227\t  //    .tz(\&quot;Asia/Hong_Kong\&quot;)\n   228\t  //    .startOf(\&quot;d\&quot;)\n   229\t  //    .toDate();\n   230\t  //  const _endDate = moment(endDate).tz(\&quot;Asia/Hong_Kong\&quot;).endOf(\&quot;d\&quot;).toDate();\n   231\t\n   232\t  const admin = await Admin.findOne({\n   233\t    email,\n   234\t  });\n   235\t\n   236\t  const firstMatchCondition = {\n   237\t    \&quot;books.collection_type\&quot;: collection_type,\n   238\t    reborrowed_once: true,\n   239\t    reborrow_one_date: {\n   240\t      $gte: new Date(startDate),\n   241\t      $lte: new Date(endDate),\n   242\t    },\n   243\t  };\n   244\t\n   245\t  const secondMatchCondition = {\n   246\t    \&quot;books.collection_type\&quot;: collection_type,\n   247\t    reborrowed_twice: true,\n   248\t    reborrow_two_date: {\n   249\t      $gte: new Date(startDate),\n   250\t      $lte: new Date(endDate),\n   251\t    },\n   252\t  };\n...\n   937\t\n   938\t  return recommendedBooks;\n   939\t}\n   940\t\n   941\tasync function getunusedBookByDateV2(\n   942\t  collection_type,\n   943\t  startDate,\n   944\t  endDate,\n   945\t  email) {\n   946\t  const _startDate = moment(startDate)\n   947\t    .tz(\&quot;Asia/Hong_Kong\&quot;)\n   948\t    .startOf(\&quot;d\&quot;)\n   949\t    .toDate();\n   950\t  const _endDate = moment(endDate)\n   951\t    .tz(\&quot;Asia/Hong_Kong\&quot;)\n   952\t    .endOf(\&quot;d\&quot;)\n   953\t    .toDate();\n   954\t\n   955\t  const splitTimePoint = new Date(\&quot;2022-11-24T16:00:00.000Z\&quot;);\n   956\t\n   957\t  const admin = await Admin.findOne({ email });\n   958\t\n   959\t  // reserve_date;\n   960\t\n   961\t  // issue_date;\n   962\t\n   963\t  const borrowedBooks = await Borrow.distinct(\&quot;book_id\&quot;, {\n   964\t    issue_date: {\n   965\t      $gte: _startDate,\n   966\t      $lte: _endDate,\n   967\t    },\n   968\t  });\n   969\t  const reservedBooks = await Reserve.distinct(\&quot;book_id\&quot;, {\n   970\t    reserve_date: {\n   971\t      $gte: _startDate,\n   972\t      $lte: _endDate,\n   973\t    },\n   974\t  });\n...\n  1295\t\n  1296\t/** 05-01-2022 -&gt; Optimised **/\n  1297\tasync function recommendedBooksbydate(book_id, startDate, endDate, all, email, isPetronVisible, collection_type) {\n  1298\t  let resx;\n  1299\t  startDate = new Date(startDate);\n  1300\t  endDate = new Date(endDate);\n  1301\t  //await (Admin.findOne({email: email}, function(err, res){ resx = (res); }).select({ _id: 1, role: 1 }).lean().exec());\n  1302\t  let catArray = await findCats();\n...\n  1733\t\n  1734\t/** 18-01-2022 -&gt; Optimised **/\n  1735\tasync function AllBooksbydate(book_id, startDate, endDate, all, email, collection_type) {\n  1736\t  let catArray = await findCats();\n  1737\t  var sortedBooks = [];\n  1738\t  var result = await Book.aggregate([{\n  1739\t    $match: {\n  1740\t      _id: {\n  1741\t        $ne: book_id\n  1742\t      },\n  1743\t      $or: [{ is_deleted: { $exists: false } }, { is_deleted: { $exists: true, $eq: false } }],\n  1744\t      added_at: {\n  1745\t        \&quot;$lte\&quot;: new Date(endDate)\n  1746\t      },\n  1747\t      collection_type\n  1748\t    }\n  1749\t  },\n  1750\t  {\n  1751\t    $group: {\n  1752\t      \&quot;_id\&quot;: {\n  1753\t        \&quot;book_id\&quot;: \&quot;$_id\&quot;,\n  1754\t        \&quot;title\&quot;: \&quot;$title\&quot;,\n  1755\t        \&quot;isbn_no\&quot;: \&quot;$isbn_no\&quot;,\n  1756\t        \&quot;author\&quot;: \&quot;$author\&quot;,\n  1757\t        \&quot;available_quantity\&quot;: \&quot;$available_quantity\&quot;,\n  1758\t        \&quot;stock_quantity\&quot;: \&quot;$stock_quantity\&quot;,\n  1759\t        \&quot;added_by\&quot;: \&quot;$added_by\&quot;,\n  1760\t        \&quot;imprints\&quot;: \&quot;$imprints\&quot;,\n  1761\t        \&quot;publishingGroup\&quot;: \&quot;$publishingGroup\&quot;,\n  1762\t        \&quot;category_id\&quot;: \&quot;$category_id\&quot;,\n  1763\t        \&quot;book_recomm\&quot;: \&quot;$book_recomm\&quot;\n  1764\t      },\n  1765\t      count: {\n  1766\t        $sum: 1\n  1767\t      }\n  1768\t    }\n  1769\t  },\n...\nPath: Routes/User.js\n...\n   188\t\n   189\tapp.post(\&quot;/getUserDetail\&quot;, upload.none(), async (req, res) =&gt; {\n   190\t\tif (req.body.user_id) {\n   191\t\t\tawait User.findOne({ _id: req.body.user_id }).then((foundData) =&gt; {\n   192\t\t\t\tres.json({\n   193\t\t\t\t\tcode: 200,\n   194\t\t\t\t\tmessage: \&quot;Operation Successful\&quot;,\n   195\t\t\t\t\tdata: {\n   196\t\t\t\t\t\tuser_id: foundData._id,\n   197\t\t\t\t\t\temail: foundData.email,\n   198\t\t\t\t\t\tphone_number: foundData.phone_number,\n   199\t\t\t\t\t\tname: foundData.name,\n   200\t\t\t\t\t}\n   201\t\t\t\t});\n   202\t\t\t\treturn res.end();\n   203\t\t\t});\n   204\t\t} else {\n   205\t\t\tres.json({\n   206\t\t\t\tcode: 422,\n   207\t\t\t\tmessage: \&quot;User ID in request\&quot;,\n   208\t\t\t\tsuccess: false,\n   209\t\t\t});\n   210\t\t\treturn res.end();\n   211\t\t}\n   212\t});\n   213\tapp.post(\&quot;/setUserDetail\&quot;, upload.none(), UserHelper, async (req, res) =&gt; {\n   214\t\tif (\n   215\t\t\t!req.body.email\n   216\t\t) {\n   217\t\t\tres.setHeader(\&quot;Content-Type\&quot;, \&quot;application/json\&quot;);\n   218\t\t\tres.status(422).json({\n   219\t\t\t\tcode: 422,\n   220\t\t\t\tmessage: \&quot;Missing Email-id\&quot;,\n   221\t\t\t\tsuccess: false,\n   222\t\t\t});\n   223\t\t\treturn res.end();\n   224\t\t}\n...\nPath: docker-entrypoint-initdb/mongo.init.js\n     1\tdb.createUser({\n     2\t  // user for database which shall be created\n     3\t  user: \&quot;service\&quot;,\n     4\t  // password of user\n     5\t  pwd: \&quot;b5f56b5df5ea1e5682dda6c1a283c91f\&quot;,\n     6\t  roles: [\n     7\t    {\n     8\t      role: \&quot;readWrite\&quot;,\n     9\t      db: \&quot;ebook\&quot;,\n    10\t    },\n    11\t  ],\n    12\t});\n...\n&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_013uJxtPuEcR1EiHfVqAPsrE&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;E:/HXL_Book/jrc-service&quot;,&quot;folderRoot&quot;:&quot;E:/HXL_Book/jrc-service&quot;}],&quot;workspaceFoldersUnchanged&quot;:false,&quot;currentTerminal&quot;:{&quot;terminalId&quot;:2,&quot;currentWorkingDirectory&quot;:&quot;E:/HXL_Book/jrc-service&quot;}}}],&quot;timestamp&quot;:&quot;2025-07-21T09:42:52.218Z&quot;,&quot;response_text&quot;:&quot;现在让我再查看一些具体的 CRUD 操作使用示例：\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;050dcc4a-ffcd-4282-9a4e-51eb3f000b00&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:0,&quot;content&quot;:&quot;基于对你项目代码的分析，我可以很好地解释 Mongoose 的 Schema 和 Model 的关系：\n\n## Mongoose Schema 和 Model 的关系\n\n你的理解基本正确！让我详细解释一下：\n\n### 1. Schema 定义结构\n&lt;augment_code_snippet path=\&quot;Model/User.js\&quot; mode=\&quot;EXCERPT\&quot;&gt;\n```javascript\nconst mongoose = require(\&quot;mongoose\&quot;);\nconst Schema = mongoose.Schema;\n\nconst User = new Schema({\n    email: {\n        required: false,\n        type: String,\n    },\n    phone_number: {\n        required: false,\n        type: String,\n    },\n    // ... 其他字段定义\n});\n```\n&lt;/augment_code_snippet&gt;\n\n### 2. Model 是 Schema 的\&quot;包装类\&quot;\n&lt;augment_code_snippet path=\&quot;Model/User.js\&quot; mode=\&quot;EXCERPT\&quot;&gt;\n```javascript\nmodule.exports = mongoose.model(\&quot;User\&quot;, User);\n```\n&lt;/augment_code_snippet&gt;\n\n这行代码做了什么：\n- `mongoose.model(\&quot;User\&quot;, User)` 创建了一个 Model 类\n- 第一个参数 `\&quot;User\&quot;` 是集合名称（MongoDB中会变成 `users`）\n- 第二个参数 `User` 是 Schema 定义\n\n### 3. Model 确实拥有 CRUD 静态方法\n\n从你的项目代码可以看到大量使用：\n\n**查询操作：**\n&lt;augment_code_snippet path=\&quot;Routes/User.js\&quot; mode=\&quot;EXCERPT\&quot;&gt;\n```javascript\nawait User.findOne({ _id: req.body.user_id })\nawait User.findOne({ email: req.body.email })\n```\n&lt;/augment_code_snippet&gt;\n\n&lt;augment_code_snippet path=\&quot;Routes/Book.js\&quot; mode=\&quot;EXCERPT\&quot;&gt;\n```javascript\nBook.findOne({\n    _id: mongoose.Types.ObjectId(req.body.id),\n    $or: [{ is_deleted: { $exists: false } }, { is_deleted: { $exists: true, $eq: false } }]\n})\n```\n&lt;/augment_code_snippet&gt;\n\n**创建操作：**\n&lt;augment_code_snippet path=\&quot;Routes/User.js\&quot; mode=\&quot;EXCERPT\&quot;&gt;\n```javascript\nconst mUser = new User({\n    email: req.body.email,\n    password: hashPassword,\n    name: req.body.name,\n    phone_number: req.body.phone_number,\n    is_active: true,\n});\n```\n&lt;/augment_code_snippet&gt;\n\n**更新操作：**\n&lt;augment_code_snippet path=\&quot;Routes/Book.js\&quot; mode=\&quot;EXCERPT\&quot;&gt;\n```javascript\nawait Book.findOneAndUpdate({\n    _id: mongoose.Types.ObjectId(req.body.book_id)\n}, {\n    is_deleted: true,\n    deleted_at: new Date()\n})\n```\n&lt;/augment_code_snippet&gt;\n\n**删除操作：**\n&lt;augment_code_snippet path=\&quot;Routes/Admin.js\&quot; mode=\&quot;EXCERPT\&quot;&gt;\n```javascript\nawait Banner.findOneAndDelete({\n    _id: req.body.delID,\n    collection_type\n})\n```\n&lt;/augment_code_snippet&gt;\n\n### 4. 两种创建实例的方式\n\n**方式1：new Model() + save()**\n&lt;augment_code_snippet path=\&quot;Routes/Reading.js\&quot; mode=\&quot;EXCERPT\&quot;&gt;\n```javascript\nconst newReading = new Reading({\n    book_id: book_id,\n    client_id: req.body.client_id,\n    ip: ip,\n    read_date: new Date(),\n    return_date: new Date(),\n    returned: false,\n});\nawait newReading.save();\n```\n&lt;/augment_code_snippet&gt;\n\n**方式2：Model.create() (静态方法)**\n```javascript\nconst newUser = await User.create({\n    email: \&quot;<EMAIL>\&quot;,\n    name: \&quot;Test User\&quot;\n});\n```\n\n### 总结\n\n你的理解完全正确：\n\n1. **Schema** = 数据结构定义（字段类型、验证规则等）\n2. **Model** = Schema 的\&quot;包装类\&quot;，提供了完整的 CRUD 静态方法\n3. **Model 的静态方法**包括：\n   - `find()`, `findOne()`, `findById()`\n   - `create()`, `insertMany()`\n   - `updateOne()`, `updateMany()`, `findOneAndUpdate()`\n   - `deleteOne()`, `deleteMany()`, `findOneAndDelete()`\n   - `countDocuments()`, `aggregate()` 等\n\n4. **Model 实例方法**包括：\n   - `save()`, `remove()`, `populate()` 等\n\n所以 Model 确实可以理解为\&quot;拥有了 CRUD 静态方法的包装类\&quot;，它让你可以直接对数据库进行操作，而不需要写原生的 MongoDB 查询。\n&quot;},{&quot;id&quot;:2,&quot;type&quot;:2,&quot;content&quot;:&quot;基于对你项目代码的分析，我可以很好地解释 Mongoose 的 Schema 和 Model 的关系：\n\n## Mongoose Schema 和 Model 的关系\n\n你的理解基本正确！让我详细解释一下：\n\n### 1. Schema 定义结构\n````javascript path=Model/User.js mode=EXCERPT\nconst mongoose = require(\&quot;mongoose\&quot;);\nconst Schema = mongoose.Schema;\n\nconst User = new Schema({\n    email: {\n        required: false,\n        type: String,\n    },\n    phone_number: {\n        required: false,\n        type: String,\n    },\n    // ... 其他字段定义\n});\n````\n\n### 2. Model 是 Schema 的\&quot;包装类\&quot;\n````javascript path=Model/User.js mode=EXCERPT\nmodule.exports = mongoose.model(\&quot;User\&quot;, User);\n````\n\n这行代码做了什么：\n- `mongoose.model(\&quot;User\&quot;, User)` 创建了一个 Model 类\n- 第一个参数 `\&quot;User\&quot;` 是集合名称（MongoDB中会变成 `users`）\n- 第二个参数 `User` 是 Schema 定义\n\n### 3. Model 确实拥有 CRUD 静态方法\n\n从你的项目代码可以看到大量使用：\n\n**查询操作：**\n````javascript path=Routes/User.js mode=EXCERPT\nawait User.findOne({ _id: req.body.user_id })\nawait User.findOne({ email: req.body.email })\n````\n\n````javascript path=Routes/Book.js mode=EXCERPT\nBook.findOne({\n    _id: mongoose.Types.ObjectId(req.body.id),\n    $or: [{ is_deleted: { $exists: false } }, { is_deleted: { $exists: true, $eq: false } }]\n})\n````\n\n**创建操作：**\n````javascript path=Routes/User.js mode=EXCERPT\nconst mUser = new User({\n    email: req.body.email,\n    password: hashPassword,\n    name: req.body.name,\n    phone_number: req.body.phone_number,\n    is_active: true,\n});\n````\n\n**更新操作：**\n````javascript path=Routes/Book.js mode=EXCERPT\nawait Book.findOneAndUpdate({\n    _id: mongoose.Types.ObjectId(req.body.book_id)\n}, {\n    is_deleted: true,\n    deleted_at: new Date()\n})\n````\n\n**删除操作：**\n````javascript path=Routes/Admin.js mode=EXCERPT\nawait Banner.findOneAndDelete({\n    _id: req.body.delID,\n    collection_type\n})\n````\n\n### 4. 两种创建实例的方式\n\n**方式1：new Model() + save()**\n````javascript path=Routes/Reading.js mode=EXCERPT\nconst newReading = new Reading({\n    book_id: book_id,\n    client_id: req.body.client_id,\n    ip: ip,\n    read_date: new Date(),\n    return_date: new Date(),\n    returned: false,\n});\nawait newReading.save();\n````\n\n**方式2：Model.create() (静态方法)**\n```javascript\nconst newUser = await User.create({\n    email: \&quot;<EMAIL>\&quot;,\n    name: \&quot;Test User\&quot;\n});\n```\n\n### 总结\n\n你的理解完全正确：\n\n1. **Schema** = 数据结构定义（字段类型、验证规则等）\n2. **Model** = Schema 的\&quot;包装类\&quot;，提供了完整的 CRUD 静态方法\n3. **Model 的静态方法**包括：\n   - `find()`, `findOne()`, `findById()`\n   - `create()`, `insertMany()`\n   - `updateOne()`, `updateMany()`, `findOneAndUpdate()`\n   - `deleteOne()`, `deleteMany()`, `findOneAndDelete()`\n   - `countDocuments()`, `aggregate()` 等\n\n4. **Model 实例方法**包括：\n   - `save()`, `remove()`, `populate()` 等\n\n所以 Model 确实可以理解为\&quot;拥有了 CRUD 静态方法的包装类\&quot;，它让你可以直接对数据库进行操作，而不需要写原生的 MongoDB 查询。\n&quot;},{&quot;id&quot;:3,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;The following code sections were retrieved:\nPath: Routes/Borrow.js\n...\n   116\t\n   117\t                const mBorrow = new Borrow({\n   118\t                    email: user.email,\n   119\t                    user_id: user._id,\n   120\t                    book_id: mongoose.Types.ObjectId(book_id),\n   121\t                    issue_date: issue_date,\n   122\t                    return_date: return_date,\n   123\t                    returned: false,\n   124\t                    is_deleted: false\n   125\t                });\n   126\t                await mBorrow.save();\n   127\t\n   128\t                await BookHelper.updateAvailableStock(book_id, 'borrow');\n   129\t                await sendBorrowMail(user.email, book.title, user.name, datetoshow);\n   130\t            }\n   131\t        }\n   132\t    }\n   133\t}\n   134\t\n   135\t/**\n   136\t * 发送借阅成功邮件\n   137\t */\n   138\tasync function sendBorrowMail(email, bookTitle, userName, returnDate) {\n   139\t    let mailOptions = {\n   140\t        to: email,\n   141\t        from: \&quot;<EMAIL>\&quot;,\n   142\t        subject: \&quot;金閱閣電子書預約書籍通知\&quot;,\n   143\t        html: `親愛的 ${userName} 讀者:&lt;br/&gt;&lt;br/&gt;\n   144\t        你在香港公共圖書館金閱閣電子書預約的《${bookTitle}》已借入你的帳戶內，期限至 ${returnDate}。\n   145\t        你可登入香港公共圖書館的 &lt;a href=\&quot;https://joyread.club\&quot;&gt;金閱閣電子書&lt;/a&gt;，透過瀏覽器線上閱讀。&lt;br/&gt;&lt;br/&gt;\n   146\t        多謝使用金閱閣電子書。`\n   147\t    };\n   148\t    await mailer(mailOptions);\n   149\t}\n...\n   212\t\n   213\tapp.post(\&quot;/book\&quot;, upload.none(), UserHelper, async (req, res) =&gt; {\n   214\t    if (\n   215\t        !req.body.user_id ||\n   216\t        !req.body.book_id ||\n   217\t        !req.body.issue_date ||\n   218\t        !req.body.return_date ||\n   219\t        !req.body.status\n   220\t    ) {\n   221\t        return res.json({\n   222\t            code: 418,\n   223\t            message: \&quot;Missing required fields\&quot;,\n   224\t            status: false,\n   225\t        });\n   226\t    }\n   227\t\n   228\t    // 同一用户同一本书只能借阅一次\n   229\t    var cacheKey = 'cache_' + req.body.user_id + '_' + req.body.book_id;\n   230\t    if (cache[cacheKey]) {\n   231\t        // 存在，表示处理中，直接返回\n   232\t        return res.json({\n   233\t            code: 418,\n   234\t            message: \&quot;Processing in progress\&quot;,\n   235\t            status: false,\n   236\t        });\n   237\t    }\n   238\t    cache[cacheKey] = cacheKey;\n   239\t\n   240\t    const _user = await User.findOne({\n   241\t        _id: new mongoose.Types.ObjectId(req.body.user_id)\n   242\t    })\n   243\t    if (!_user) {\n   244\t        const systemLog = SystemLog({\n   245\t            ctime: new Date(),\n   246\t            data: {\n   247\t                body: req.body,\n   248\t                token: req.header(\&quot;SESSION-TOKEN\&quot;)\n   249\t            }\n   250\t        })\n   251\t        await systemLog.save()\n   252\t    }\n...\n   322\t                } else {\n   323\t                    var user_id = req.body.user_id;\n   324\t                    var book_id = req.body.book_id;\n   325\t\n   326\t                    const mBorrow = Borrow({\n   327\t                        email: req.body.email,\n   328\t                        user_id: req.body.user_id,\n   329\t                        book_id: req.body.book_id,\n   330\t                        issue_date: issue_date,\n   331\t                        return_date: return_date,\n   332\t                        returned: req.body.status,\n   333\t                        is_deleted: false\n   334\t                    });\n   335\t\n   336\t                    mBorrow\n   337\t                        .save()\n   338\t                        .then((stored) =&gt; {\n   339\t                            (BookHelper.updateAvailableStock(book_id, 'borrow'));\n   340\t                            Reserve.findOneAndUpdate({\n   341\t                                book_id: req.body.book_id,\n   342\t                                is_deleted: false,\n   343\t                                user_id: req.body.user_id\n   344\t                            }, {\n   345\t                                is_deleted: true\n   346\t                            }, {\n   347\t                                useFindAndModify: false\n   348\t                            },\n   349\t                                (err, updated) =&gt; { });\n   350\t                            User.findOne({\n   351\t                                _id: user_id\n   352\t                            }).then((userData) =&gt; {\n   353\t                                if (userData) {\n   354\t                                    var email = req.body.email;\n   355\t                                    var name = userData.name;\n   356\t                                    Book.findOne({\n   357\t                                        _id: book_id\n   358\t                                    }).then((bookData) =&gt; {\n...\n   543\t\n   544\tapp.post(\&quot;/deleteBook\&quot;, upload.none(), UserHelper, async (req, res) =&gt; {\n   545\t    if (!req.body.delete_id) {\n   546\t        return res.json({\n   547\t            code: 422,\n   548\t            message: \&quot;Missing required fields\&quot;,\n   549\t            status: false,\n   550\t        });\n   551\t    }\n   552\t    let doc = await Borrow.findOneAndUpdate({\n   553\t        _id: req.body.delete_id\n   554\t    }, {\n   555\t        is_deleted: true\n   556\t    });\n   557\t\n   558\t    return res.json({\n   559\t        code: 200,\n   560\t        data: false,\n   561\t        message: \&quot;Operation successful.\&quot;,\n   562\t    });\n   563\t});\n...\nPath: Routes/Book.js\n     1\tconst app = require(\&quot;express\&quot;).Router();\n     2\tvar multer = require(\&quot;multer\&quot;);\n     3\tconst Admin = require(\&quot;../Model/Admin\&quot;);\n     4\tconst { AdminHelper } = require(\&quot;../Helper/AdminHelper\&quot;);\n     5\tconst { UserHelper } = require(\&quot;../Helper/UserHelper\&quot;);\n     6\tconst Book = require(\&quot;../Model/Book\&quot;);\n     7\tconst Banner = require(\&quot;../Model/Banner\&quot;);\n     8\tconst Category = require(\&quot;../Model/Category\&quot;);\n     9\tconst fs = require(\&quot;fs\&quot;);\n    10\tconst AdminSession = require(\&quot;../Model/AdminSession\&quot;);\n    11\tconst UserSession = require(\&quot;../Model/UserSession\&quot;);\n    12\tconst Borrow = require(\&quot;../Model/Borrow\&quot;);\n    13\tconst Reading = require(\&quot;../Model/Reading\&quot;);\n    14\tconst Reserve = require(\&quot;../Model/Reserve\&quot;);\n    15\tconst Favourite = require(\&quot;../Model/Favourite\&quot;);\n    16\tconst Preview = require(\&quot;../Model/Preview\&quot;);\n    17\t// const cron = require('node-cron');\n...\n   839\t\n   840\tapp.post(\&quot;/delete\&quot;, AdminHelper, upload.none(), async (req, res) =&gt; {\n   841\t\tif (!req.body.book_id) {\n   842\t\t\tres.json({\n   843\t\t\t\tcode: 422,\n   844\t\t\t\tmessage: \&quot;Missing fields in request\&quot;,\n   845\t\t\t\tsuccess: false,\n   846\t\t\t});\n   847\t\t\treturn res.end();\n   848\t\t}\n   849\t\n   850\t\tawait Book.findOneAndUpdate({\n   851\t\t\t_id: mongoose.Types.ObjectId(req.body.book_id)\n   852\t\t}, {\n   853\t\t\tis_deleted: true,\n   854\t\t\tdeleted_at: new Date()\n   855\t\t}, {\n   856\t\t\tuseFindAndModify: false\n   857\t\t},\n   858\t\t\tasync (err, updated) =&gt; {\n   859\t\t\t\tif (err) {\n   860\t\t\t\t\treturn res.json({\n   861\t\t\t\t\t\tcode: 500,\n   862\t\t\t\t\t\tmessage: \&quot;Internal error at updating book-cover\&quot;,\n   863\t\t\t\t\t\tsuccess: false,\n   864\t\t\t\t\t});\n   865\t\t\t\t}\n   866\t\n   867\t\t\t\tvar countsborrow = await Borrow.find({\n   868\t\t\t\t\tis_deleted: false,\n   869\t\t\t\t\treturned: false,\n   870\t\t\t\t\tbook_id: mongoose.Types.ObjectId(req.body.book_id)\n   871\t\t\t\t}).countDocuments();\n...\n  3364\t\t\t\t\t\tawait Book.findByIdAndUpdate(\n  3365\t\t\t\t\t\t\treq.body.id, {\n  3366\t\t\t\t\t\t\tavailable_quantity: newAvailableQuantity\n  3367\t\t\t\t\t\t}, {\n  3368\t\t\t\t\t\t\tuseFindAndModify: false\n  3369\t\t\t\t\t\t},\n  3370\t\t\t\t\t\t\t(err, updated) =&gt; {\n  3371\t\t\t\t\t\t\t\tif (err)\n  3372\t\t\t\t\t\t\t\t\tconsole.log(\&quot;Internal error in update qty.\&quot;);\n  3373\t\n  3374\t\t\t\t\t\t\t\tconsole.log(\&quot;Update qty successful.\&quot;);\n  3375\t\t\t\t\t\t\t}\n  3376\t\t\t\t\t\t);\n  3377\t\t\t\t\t}\n  3378\t\n  3379\t\t\t\t\tawait Book.findByIdAndUpdate(\n  3380\t\t\t\t\t\treq.body.id, {\n  3381\t\t\t\t\t\t[req.body.field]: req.body.value\n  3382\t\t\t\t\t}, {\n  3383\t\t\t\t\t\tuseFindAndModify: false\n  3384\t\t\t\t\t},\n  3385\t\t\t\t\t\t(err, updated) =&gt; {\n  3386\t\t\t\t\t\t\tif (err)\n  3387\t\t\t\t\t\t\t\treturn res.json({\n  3388\t\t\t\t\t\t\t\t\tcode: 500,\n  3389\t\t\t\t\t\t\t\t\tmessage: \&quot;Internal error.\&quot;,\n  3390\t\t\t\t\t\t\t\t\tsuccess: false,\n  3391\t\t\t\t\t\t\t\t});\n  3392\t\t\t\t\t\t\treturn res.json({\n  3393\t\t\t\t\t\t\t\tcode: 200,\n  3394\t\t\t\t\t\t\t\tdata: updated,\n  3395\t\t\t\t\t\t\t\tmessage: \&quot;Operation successful\&quot;,\n  3396\t\t\t\t\t\t\t});\n  3397\t\t\t\t\t\t}\n  3398\t\t\t\t\t);\n  3399\t\n  3400\t\t\t\t}\n  3401\t\t\t} catch (e) {\n  3402\t\t\t\tconsole.log(e.message);\n  3403\t\t\t\tres.json({\n  3404\t\t\t\t\tcode: 422,\n  3405\t\t\t\t\tmessage: e.message,\n  3406\t\t\t\t\tsuccess: false,\n  3407\t\t\t\t});\n  3408\t\t\t\treturn res.end();\n  3409\t\t\t}\n  3410\t\t}\n  3411\t);\n...\nPath: Routes/Category.js\n...\n    84\t\n    85\tapp.post(\&quot;/edit\&quot;, AdminHelper, upload.none(), async (req, res) =&gt; {\n    86\t\tif (!req.body.name || !req.body.category_id) {\n    87\t\t\tres.json({\n    88\t\t\t\tcode: 422,\n    89\t\t\t\tmessage: \&quot;Missing fields in request\&quot;,\n    90\t\t\t\tsuccess: false,\n    91\t\t\t});\n    92\t\t\treturn res.end();\n    93\t\t}\n    94\t\tawait Category.findOneAndUpdate(\n    95\t\t\t{ _id: req.body.category_id },\n    96\t\t\t{ name: req.body.name }\n    97\t\t).then((deletedOne) =&gt; {\n    98\t\t\treturn res.json({\n    99\t\t\t\tcode: 200,\n   100\t\t\t\tmessage: \&quot;Modification successful\&quot;,\n   101\t\t\t\tstatus: true,\n   102\t\t\t});\n   103\t\t});\n   104\t});\n   105\t\n   106\tapp.post(\&quot;/create\&quot;, upload.none(), AdminHelper, async (req, res) =&gt; {\n   107\t\tif (!req.body.name) {\n   108\t\t\tres.json({\n   109\t\t\t\tcode: 422,\n   110\t\t\t\tmessage: \&quot;Missing fields in request\&quot;,\n   111\t\t\t\tsuccess: false,\n   112\t\t\t});\n   113\t\t\treturn res.end();\n   114\t\t}\n   115\t\tUserSession.findOne;\n   116\t\tconst mCategory = new Category({\n   117\t\t\tname: req.body.name,\n   118\t\t});\n   119\t\tawait Category.findOne({ name: {'$regex': req.body.name, '$options': 'i'}} ).then(async(ress) =&gt; {\n   120\t\t\tif(!ress)\n   121\t\t\t{\n   122\t\t\t\tawait mCategory\n   123\t\t\t\t.save()\n   124\t\t\t\t.then((storedData) =&gt; {\n   125\t\t\t\t\tres.json({\n   126\t\t\t\t\t\tcode: 200,\n   127\t\t\t\t\t\tmessage: \&quot;Create operation successful\&quot;,\n   128\t\t\t\t\t\tdata: {\n   129\t\t\t\t\t\t\tid: storedData._id,\n   130\t\t\t\t\t\t\tname: storedData.name,\n   131\t\t\t\t\t\t},\n   132\t\t\t\t\t});\n   133\t\t\t\t\tres.end();\n   134\t\t\t\t})\n   135\t\t\t\t.catch((err) =&gt; {\n   136\t\t\t\t\tres.json({\n   137\t\t\t\t\t\tmessage: \&quot;Internal error in creating category.\&quot;,\n   138\t\t\t\t\t\tsuccess: false,\n   139\t\t\t\t\t\tcode: 500,\n   140\t\t\t\t\t});\n   141\t\t\t\t\tres.end();\n   142\t\t\t\t});\n   143\t\t\t}\n   144\t\t\telse{\n   145\t\t\t\t\n   146\t\t\t\t\tres.json({\n   147\t\t\t\t\t\tmessage: \&quot;Category Already Exist !\&quot;,\n   148\t\t\t\t\t\tsuccess: false,\n   149\t\t\t\t\t\tcode: 500,\n   150\t\t\t\t\t});\n   151\t\t\t\t\tres.end();\n   152\t\t\t\t\n   153\t\t\t}\n   154\t\t});\n   155\t\t\n   156\t});\n   157\tmodule.exports = {\n   158\t    router: app\n   159\t};\n...\nPath: Routes/Reading.js\n...\n    48\t\n    49\t    if (existingReading) {\n    50\t        console.log(`Client ${req.body.client_id} already has active reading for book ${book_id}, reading_id: ${existingReading._id}`);\n    51\t        res.json({\n    52\t            code: 200,\n    53\t            message: \&quot;Book start reading\&quot;,\n    54\t            data: {\n    55\t                reading_id: existingReading._id,\n    56\t            },\n    57\t        });\n    58\t\n    59\t        return res.end();\n    60\t    }\n    61\t    books_stock = await BookHelper.isBookInStock(book_id);\n    62\t    if (books_stock &lt;= 0) {\n    63\t        res.json({\n    64\t            code: 401,\n    65\t            message: \&quot;Book not has stock\&quot;,\n    66\t            status: false,\n    67\t        });\n    68\t        return res.end();\n    69\t    }; \n    70\t\n    71\t    const newReading = new Reading({\n    72\t        book_id: book_id,\n    73\t        client_id: req.body.client_id,\n    74\t        ip: ip,\n    75\t        read_date: new Date(),\n    76\t        return_date: new Date(),\n    77\t        returned: false,\n    78\t    });\n    79\t    await newReading.save();\n    80\t\n    81\t    const newReadingRecord = new ReadingRecord({\n    82\t        reading_id: newReading._id,\n    83\t        read_time: new Date(),\n    84\t    });\n    85\t    await newReadingRecord.save();\n    86\t\n    87\t    console.log(`New reading started - Client: ${req.body.client_id}, Book: ${book_id}, Reading ID: ${newReading._id}, IP: ${ip}`);\n    88\t    console.log(\&quot;record id is:\&quot;,newReadingRecord._id);\n    89\t\n    90\t    // 更新库存前记录当前库存状态\n    91\t    const bookBefore = await BookHelper.isBookInStock(book_id);\n    92\t    await BookHelper.updateAvailableStock(book_id, 'reading');\n    93\t    const bookAfter = await BookHelper.isBookInStock(book_id);\n    94\t    console.log(`Stock updated for book ${book_id}: ${bookBefore} -&gt; ${bookAfter}`);\n    95\t\n    96\t    res.json({\n    97\t        code: 200,\n    98\t        message: \&quot;Book start reading\&quot;,\n    99\t        data: {\n   100\t            reading_id: newReading._id,\n   101\t        },\n   102\t    });\n   103\t\n   104\t    return res.end();\n   105\t\n   106\t});\n   107\t\n   108\tapp.post(\&quot;/record\&quot;, upload.none(), async (req, res) =&gt; {\n   109\t    if (!req.body.reading_id) {\n   110\t        return res.json({\n   111\t            code: 422,\n   112\t            message: \&quot;Missing required fields \&quot;,\n   113\t            status: false,\n   114\t        });\n   115\t    }\n   116\t    await Reading.findOne({\n   117\t        _id: req.body.reading_id,\n   118\t        returned: false,\n   119\t    }).then(async (resx) =&gt; {\n   120\t        if (!resx) {\n   121\t            return res.json({\n   122\t                code: 422,\n   123\t                message: \&quot;No data found\&quot;,\n   124\t                status: false,\n   125\t            });\n   126\t        } \n   127\t    });\n   128\t    await ReadingRecord.findOneAndUpdate({\n   129\t        reading_id: mongoose.Types.ObjectId(req.body.reading_id)\n   130\t    }, {\n   131\t        read_time: new Date()\n   132\t    }, {\n   133\t        upsert: true \n   134\t    });\n   135\t    res.json({\n   136\t        code: 200,\n   137\t        message: \&quot;Reading record created\&quot;,\n   138\t        status: true,\n   139\t    });\n   140\t\n   141\t    return res.end();\n   142\t});\n...\nPath: Routes/Admin.js\n...\n    85\t\n    86\tapp.post(\&quot;/settings\&quot;, AdminHelper, async (req, res) =&gt; {\n    87\t\tconst collection_type = req.header(\&quot;x-current-collection\&quot;) || \&quot;JYG\&quot;;\n    88\t\n    89\t\tconst { key, value } = req.body;\n    90\t\n    91\t\tawait Setting.findOne({ key, collection_type }).then(async (resx) =&gt; {\n    92\t\t\tif (resx) {\n    93\t\t\t\tawait Setting.updateOne(\n    94\t\t\t\t\t{ key, collection_type },\n    95\t\t\t\t\t{ $set: { value } },\n    96\t\t\t\t\t{ upsert: false }\n    97\t\t\t\t);\n    98\t\t\t} else {\n    99\t\t\t\tawait Setting.insertMany([{ key, value, collection_type }]);\n   100\t\t\t}\n   101\t\t\tres.json({\n   102\t\t\t\tcode: 200,\n   103\t\t\t\tdata: {\n   104\t\t\t\t\tkey,\n   105\t\t\t\t\tvalue,\n   106\t\t\t\t\tcollection_type,\n   107\t\t\t\t},\n   108\t\t\t\tmessage: \&quot;Operation Successful\&quot;,\n   109\t\t\t});\n   110\t\t\treturn res.end();\n   111\t\t});\n   112\t});\n...\n   125\t\tawait Admin.findOne({ email: req.body.email, collection_type }).then(async (resx) =&gt; {\n   126\t\t\tif (resx) {\n   127\t\t\t\tres.json({\n   128\t\t\t\t\tcode: 409,\n   129\t\t\t\t\tmessage: \&quot;Email already exists.\&quot;,\n   130\t\t\t\t\tsuccess: false,\n   131\t\t\t\t});\n   132\t\t\t\treturn res.end();\n   133\t\t\t} else {\n   134\t\t\t\tconst salt = await bcrypt.genSalt(10);\n   135\t\t\t\tconst hash = await bcrypt.hash(req.body.password, salt);\n   136\t\t\t\t// 2 : librarian, 3: Publisher\n   137\t\t\t\tvar super_admin = false; var role = false; var role = req.body.role;\n   138\t\t\t\tif (req.body.role == '1') { super_admin = true; }\n   139\t\t\t\tconst mAdmin = Admin({\n   140\t\t\t\t\tname: req.body.name,\n   141\t\t\t\t\temail: req.body.email,\n   142\t\t\t\t\tphone_number: req.body.phone,\n   143\t\t\t\t\tpassword: hash,\n   144\t\t\t\t\tis_active: true,\n   145\t\t\t\t\tsuper_admin: super_admin,\n   146\t\t\t\t\trole: role,\n   147\t\t\t\t\tcollection_type\n   148\t\t\t\t});\n...\n   499\t\n   500\tapp.post(\&quot;/deleteBanner\&quot;, AdminHelper, async function (req, res) {\n   501\t\tconsole.log(req.body);\n   502\t\tconst collection_type = req.header(\&quot;x-current-collection\&quot;) || \&quot;JYG\&quot;;\n   503\t\tif (req.body.delID) {\n   504\t\t\tawait Banner.findOneAndDelete({\n   505\t\t\t\t_id: req.body.delID,\n   506\t\t\t\tcollection_type\n   507\t\t\t}).then(async (resx) =&gt; {\n   508\t\t\t\tlet bannerimage = resx &amp;&amp; resx.bannerimage ? resx.bannerimage : '';\n   509\t\t\t\tif (fs.existsSync(bannerimage)) {\n   510\t\t\t\t\tfs.unlink(bannerimage, (err) =&gt; {\n   511\t\t\t\t\t\tif (err) {\n   512\t\t\t\t\t\t\treturn res.json({\n   513\t\t\t\t\t\t\t\tcode: 500,\n   514\t\t\t\t\t\t\t\tmessage: \&quot;Internal error at delete preview\&quot;,\n   515\t\t\t\t\t\t\t\tsuccess: false,\n   516\t\t\t\t\t\t\t});\n   517\t\t\t\t\t\t}\n   518\t\t\t\t\t});\n   519\t\t\t\t}\n   520\t\n   521\t\t\t\treturn res.json({ code: 200, message: \&quot;Banner deleted successfully\&quot; });\n   522\t\t\t});\n   523\t\t}\n   524\t\treturn res.json({ code: 422, message: \&quot;Banners Not Available.\&quot; });\n   525\t});\n...\nPath: Routes/Reserve.js\n...\n   172\tapp.post(\&quot;/allReservedeleteBook\&quot;, UserHelper, async (req, res) =&gt; {\n   173\t\tif (!req.body.delete_id) {\n   174\t\t\treturn res.json({\n   175\t\t\t\tcode: 422,\n   176\t\t\t\tmessage: \&quot;Missing required fields\&quot;,\n   177\t\t\t\tstatus: false,\n   178\t\t\t});\n   179\t\t}\n   180\t\tvar bookbulk = [];\n   181\t\n   182\t\n   183\t\tconst promises1 = req.body.delete_id.map(async (obj) =&gt; {\n   184\t\t\tbookbulk.push(obj);\n   185\t\t});\n   186\t\tconst results1 = await Promise.all(promises1);\n   187\t\tconst options = { ordered: true };\n   188\t\tvar query = { _id: bookbulk };\n   189\t\tvar data = { $set: { is_deleted: true } }\n   190\t\tconst result = await Reserve.updateMany(query, data);\n   191\t\treturn res.json({\n   192\t\t\tcode: 200,\n   193\t\t\tdata: req.body.delete_id,\n   194\t\t\tmessage: \&quot;Operation successful.\&quot;,\n   195\t\t});\n   196\t\n   197\t});\n   198\tmodule.exports = {\n   199\t    router: app\n   200\t};...\nPath: Routes/User.js\n...\n   115\t\n   116\tapp.post(\&quot;/register\&quot;, upload.none(), async (req, res) =&gt; {\n   117\t\tif (\n   118\t\t\t!req.body.email ||\n   119\t\t\t!req.body.password ||\n   120\t\t\t!req.body.name ||\n   121\t\t\t!req.body.phone_number\n   122\t\t) {\n   123\t\t\tres.setHeader(\&quot;Content-Type\&quot;, \&quot;application/json\&quot;);\n   124\t\t\tres.status(422).json({\n   125\t\t\t\tcode: 422,\n   126\t\t\t\tmessage: \&quot;Missing fields in request\&quot;,\n   127\t\t\t\tsuccess: false,\n   128\t\t\t});\n   129\t\t\treturn res.end();\n   130\t\t}\n   131\t\tawait User.findOne({ email: req.body.email }).then(async (resx) =&gt; {\n   132\t\t\tif (resx) {\n   133\t\t\t\tres.json({\n   134\t\t\t\t\tcode: 409,\n   135\t\t\t\t\tmessage: \&quot;Email already exists.\&quot;,\n   136\t\t\t\t\tsuccess: false,\n   137\t\t\t\t});\n   138\t\t\t\treturn res.end();\n   139\t\t\t} else {\n   140\t\t\t\tconst salt = await bcrypt.genSalt(10);\n   141\t\t\t\tconst hashPassword = await bcrypt.hash(req.body.password, salt);\n   142\t\n   143\t\t\t\tconst mUser = new User({\n   144\t\t\t\t\temail: req.body.email,\n   145\t\t\t\t\tpassword: hashPassword,\n   146\t\t\t\t\tname: req.body.name,\n   147\t\t\t\t\tphone_number: req.body.phone_number,\n   148\t\t\t\t\tis_active: true,\n   149\t\t\t\t});\n...\n   188\t\n   189\tapp.post(\&quot;/getUserDetail\&quot;, upload.none(), async (req, res) =&gt; {\n   190\t\tif (req.body.user_id) {\n   191\t\t\tawait User.findOne({ _id: req.body.user_id }).then((foundData) =&gt; {\n   192\t\t\t\tres.json({\n   193\t\t\t\t\tcode: 200,\n   194\t\t\t\t\tmessage: \&quot;Operation Successful\&quot;,\n   195\t\t\t\t\tdata: {\n   196\t\t\t\t\t\tuser_id: foundData._id,\n   197\t\t\t\t\t\temail: foundData.email,\n   198\t\t\t\t\t\tphone_number: foundData.phone_number,\n   199\t\t\t\t\t\tname: foundData.name,\n   200\t\t\t\t\t}\n   201\t\t\t\t});\n   202\t\t\t\treturn res.end();\n   203\t\t\t});\n   204\t\t} else {\n   205\t\t\tres.json({\n   206\t\t\t\tcode: 422,\n   207\t\t\t\tmessage: \&quot;User ID in request\&quot;,\n   208\t\t\t\tsuccess: false,\n   209\t\t\t});\n   210\t\t\treturn res.end();\n   211\t\t}\n   212\t});\n   213\tapp.post(\&quot;/setUserDetail\&quot;, upload.none(), UserHelper, async (req, res) =&gt; {\n   214\t\tif (\n   215\t\t\t!req.body.email\n   216\t\t) {\n   217\t\t\tres.setHeader(\&quot;Content-Type\&quot;, \&quot;application/json\&quot;);\n   218\t\t\tres.status(422).json({\n   219\t\t\t\tcode: 422,\n   220\t\t\t\tmessage: \&quot;Missing Email-id\&quot;,\n   221\t\t\t\tsuccess: false,\n   222\t\t\t});\n   223\t\t\treturn res.end();\n   224\t\t}\n...\n&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_vrtx_016KqiWujfThyiePoLCJttzf&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;E:/HXL_Book/jrc-service&quot;,&quot;folderRoot&quot;:&quot;E:/HXL_Book/jrc-service&quot;}],&quot;workspaceFoldersUnchanged&quot;:false,&quot;currentTerminal&quot;:{&quot;terminalId&quot;:2,&quot;currentWorkingDirectory&quot;:&quot;E:/HXL_Book/jrc-service&quot;}}}],&quot;timestamp&quot;:&quot;2025-07-21T09:43:26.356Z&quot;,&quot;response_text&quot;:&quot;基于对你项目代码的分析，我可以很好地解释 Mongoose 的 Schema 和 Model 的关系：\n\n## Mongoose Schema 和 Model 的关系\n\n你的理解基本正确！让我详细解释一下：\n\n### 1. Schema 定义结构\n````javascript path=Model/User.js mode=EXCERPT\nconst mongoose = require(\&quot;mongoose\&quot;);\nconst Schema = mongoose.Schema;\n\nconst User = new Schema({\n    email: {\n        required: false,\n        type: String,\n    },\n    phone_number: {\n        required: false,\n        type: String,\n    },\n    // ... 其他字段定义\n});\n````\n\n### 2. Model 是 Schema 的\&quot;包装类\&quot;\n````javascript path=Model/User.js mode=EXCERPT\nmodule.exports = mongoose.model(\&quot;User\&quot;, User);\n````\n\n这行代码做了什么：\n- `mongoose.model(\&quot;User\&quot;, User)` 创建了一个 Model 类\n- 第一个参数 `\&quot;User\&quot;` 是集合名称（MongoDB中会变成 `users`）\n- 第二个参数 `User` 是 Schema 定义\n\n### 3. Model 确实拥有 CRUD 静态方法\n\n从你的项目代码可以看到大量使用：\n\n**查询操作：**\n````javascript path=Routes/User.js mode=EXCERPT\nawait User.findOne({ _id: req.body.user_id })\nawait User.findOne({ email: req.body.email })\n````\n\n````javascript path=Routes/Book.js mode=EXCERPT\nBook.findOne({\n    _id: mongoose.Types.ObjectId(req.body.id),\n    $or: [{ is_deleted: { $exists: false } }, { is_deleted: { $exists: true, $eq: false } }]\n})\n````\n\n**创建操作：**\n````javascript path=Routes/User.js mode=EXCERPT\nconst mUser = new User({\n    email: req.body.email,\n    password: hashPassword,\n    name: req.body.name,\n    phone_number: req.body.phone_number,\n    is_active: true,\n});\n````\n\n**更新操作：**\n````javascript path=Routes/Book.js mode=EXCERPT\nawait Book.findOneAndUpdate({\n    _id: mongoose.Types.ObjectId(req.body.book_id)\n}, {\n    is_deleted: true,\n    deleted_at: new Date()\n})\n````\n\n**删除操作：**\n````javascript path=Routes/Admin.js mode=EXCERPT\nawait Banner.findOneAndDelete({\n    _id: req.body.delID,\n    collection_type\n})\n````\n\n### 4. 两种创建实例的方式\n\n**方式1：new Model() + save()**\n````javascript path=Routes/Reading.js mode=EXCERPT\nconst newReading = new Reading({\n    book_id: book_id,\n    client_id: req.body.client_id,\n    ip: ip,\n    read_date: new Date(),\n    return_date: new Date(),\n    returned: false,\n});\nawait newReading.save();\n````\n\n**方式2：Model.create() (静态方法)**\n```javascript\nconst newUser = await User.create({\n    email: \&quot;<EMAIL>\&quot;,\n    name: \&quot;Test User\&quot;\n});\n```\n\n### 总结\n\n你的理解完全正确：\n\n1. **Schema** = 数据结构定义（字段类型、验证规则等）\n2. **Model** = Schema 的\&quot;包装类\&quot;，提供了完整的 CRUD 静态方法\n3. **Model 的静态方法**包括：\n   - `find()`, `findOne()`, `findById()`\n   - `create()`, `insertMany()`\n   - `updateOne()`, `updateMany()`, `findOneAndUpdate()`\n   - `deleteOne()`, `deleteMany()`, `findOneAndDelete()`\n   - `countDocuments()`, `aggregate()` 等\n\n4. **Model 实例方法**包括：\n   - `save()`, `remove()`, `populate()` 等\n\n所以 Model 确实可以理解为\&quot;拥有了 CRUD 静态方法的包装类\&quot;，它让你可以直接对数据库进行操作，而不需要写原生的 MongoDB 查询。\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;aedd7cd4-9d51-4a60-8b42-9ed88cb3fc67&quot;,&quot;request_message&quot;:&quot;    let borrows = await Borrow.find(condition).lean()\r\nlean 的含义&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;    let borrows = await Borrow.find(condition).lean()\r&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;lean 的含义&quot;}]}]},&quot;mentioned_items&quot;:[],&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:0,&quot;content&quot;:&quot;`lean()` 是 Mongoose 的一个重要方法，让我解释一下它的含义和作用：\n\n## lean() 的含义\n\n`lean()` 方法告诉 Mongoose 返回**纯 JavaScript 对象**，而不是 Mongoose 文档实例。\n\n### 1. 默认情况（不使用 lean）\n\n&lt;augment_code_snippet path=\&quot;Routes/Borrow.js\&quot; mode=\&quot;EXCERPT\&quot;&gt;\n```javascript\n// 返回 Mongoose 文档实例\nlet borrows = await Borrow.find(condition)\n// borrows[0] 是一个 Mongoose Document，包含很多方法\n```\n&lt;/augment_code_snippet&gt;\n\n### 2. 使用 lean() 后\n\n&lt;augment_code_snippet path=\&quot;Routes/Borrow.js\&quot; mode=\&quot;EXCERPT\&quot;&gt;\n```javascript\n// 返回纯 JavaScript 对象\nlet borrows = await Borrow.find(condition).lean()\n// borrows[0] 是一个普通的 JS 对象，只包含数据\n```\n&lt;/augment_code_snippet&gt;\n\n## 主要区别\n\n### 不使用 lean()：\n```javascript\nlet borrow = await Borrow.findOne({_id: \&quot;123\&quot;})\nconsole.log(borrow.constructor.name) // \&quot;Document\&quot;\n\n// 可以使用 Mongoose 方法\nawait borrow.save()\nawait borrow.populate('user_id')\nborrow.toJSON()\n```\n\n### 使用 lean()：\n```javascript\nlet borrow = await Borrow.findOne({_id: \&quot;123\&quot;}).lean()\nconsole.log(borrow.constructor.name) // \&quot;Object\&quot;\n\n// 这些方法不可用，因为它只是普通对象\n// borrow.save() // ❌ 错误！\n// borrow.populate() // ❌ 错误！\n```\n\n## lean() 的优势\n\n### 1. **性能更好**\n- 跳过 Mongoose 的文档实例化过程\n- 内存占用更少\n- 查询速度更快\n\n### 2. **数据更轻量**\n- 没有额外的 Mongoose 方法和属性\n- 序列化更快（如转 JSON）\n\n### 3. **适合只读操作**\n- 当你只需要读取数据，不需要修改时\n\n## 使用场景\n\n### ✅ 适合使用 lean() 的场景：\n```javascript\n// 1. 只是读取数据用于显示\nlet books = await Book.find({}).lean()\nres.json(books)\n\n// 2. 数据统计和分析\nlet stats = await Borrow.find({returned: false}).lean()\nlet count = stats.length\n\n// 3. 大量数据查询（性能优化）\nlet allBorrows = await Borrow.find({}).lean()\n```\n\n### ❌ 不适合使用 lean() 的场景：\n```javascript\n// 1. 需要修改数据\nlet borrow = await Borrow.findOne({_id: id}) // 不用 lean\nborrow.returned = true\nawait borrow.save()\n\n// 2. 需要使用 populate 等 Mongoose 功能\nlet borrow = await Borrow.findOne({_id: id}).populate('user_id') // 不用 lean\n\n// 3. 需要数据验证和中间件\nlet borrow = new Borrow(data) // 不用 lean\nawait borrow.save() // 会触发验证和中间件\n```\n\n## 在你的项目中\n\n从你选中的代码来看：\n```javascript\nlet borrows = await Borrow.find(condition).lean()\n```\n\n这里使用 `lean()` 很合适，因为看起来是在获取借阅记录用于展示或统计，不需要修改这些数据，所以使用 `lean()` 可以提高性能。\n\n**总结**：`lean()` = \&quot;我只要数据，不要 Mongoose 的额外功能\&quot;，换取更好的性能。\n&quot;},{&quot;id&quot;:2,&quot;type&quot;:2,&quot;content&quot;:&quot;`lean()` 是 Mongoose 的一个重要方法，让我解释一下它的含义和作用：\n\n## lean() 的含义\n\n`lean()` 方法告诉 Mongoose 返回**纯 JavaScript 对象**，而不是 Mongoose 文档实例。\n\n### 1. 默认情况（不使用 lean）\n\n````javascript path=Routes/Borrow.js mode=EXCERPT\n// 返回 Mongoose 文档实例\nlet borrows = await Borrow.find(condition)\n// borrows[0] 是一个 Mongoose Document，包含很多方法\n````\n\n### 2. 使用 lean() 后\n\n````javascript path=Routes/Borrow.js mode=EXCERPT\n// 返回纯 JavaScript 对象\nlet borrows = await Borrow.find(condition).lean()\n// borrows[0] 是一个普通的 JS 对象，只包含数据\n````\n\n## 主要区别\n\n### 不使用 lean()：\n```javascript\nlet borrow = await Borrow.findOne({_id: \&quot;123\&quot;})\nconsole.log(borrow.constructor.name) // \&quot;Document\&quot;\n\n// 可以使用 Mongoose 方法\nawait borrow.save()\nawait borrow.populate('user_id')\nborrow.toJSON()\n```\n\n### 使用 lean()：\n```javascript\nlet borrow = await Borrow.findOne({_id: \&quot;123\&quot;}).lean()\nconsole.log(borrow.constructor.name) // \&quot;Object\&quot;\n\n// 这些方法不可用，因为它只是普通对象\n// borrow.save() // ❌ 错误！\n// borrow.populate() // ❌ 错误！\n```\n\n## lean() 的优势\n\n### 1. **性能更好**\n- 跳过 Mongoose 的文档实例化过程\n- 内存占用更少\n- 查询速度更快\n\n### 2. **数据更轻量**\n- 没有额外的 Mongoose 方法和属性\n- 序列化更快（如转 JSON）\n\n### 3. **适合只读操作**\n- 当你只需要读取数据，不需要修改时\n\n## 使用场景\n\n### ✅ 适合使用 lean() 的场景：\n```javascript\n// 1. 只是读取数据用于显示\nlet books = await Book.find({}).lean()\nres.json(books)\n\n// 2. 数据统计和分析\nlet stats = await Borrow.find({returned: false}).lean()\nlet count = stats.length\n\n// 3. 大量数据查询（性能优化）\nlet allBorrows = await Borrow.find({}).lean()\n```\n\n### ❌ 不适合使用 lean() 的场景：\n```javascript\n// 1. 需要修改数据\nlet borrow = await Borrow.findOne({_id: id}) // 不用 lean\nborrow.returned = true\nawait borrow.save()\n\n// 2. 需要使用 populate 等 Mongoose 功能\nlet borrow = await Borrow.findOne({_id: id}).populate('user_id') // 不用 lean\n\n// 3. 需要数据验证和中间件\nlet borrow = new Borrow(data) // 不用 lean\nawait borrow.save() // 会触发验证和中间件\n```\n\n## 在你的项目中\n\n从你选中的代码来看：\n```javascript\nlet borrows = await Borrow.find(condition).lean()\n```\n\n这里使用 `lean()` 很合适，因为看起来是在获取借阅记录用于展示或统计，不需要修改这些数据，所以使用 `lean()` 可以提高性能。\n\n**总结**：`lean()` = \&quot;我只要数据，不要 Mongoose 的额外功能\&quot;，换取更好的性能。\n&quot;},{&quot;id&quot;:3,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;text_node&quot;:{&quot;content&quot;:&quot;    let borrows = await Borrow.find(condition).lean()\r\nlean 的含义&quot;}},{&quot;id&quot;:1,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;E:/HXL_Book/jrc-service&quot;,&quot;folderRoot&quot;:&quot;E:/HXL_Book/jrc-service&quot;}],&quot;workspaceFoldersUnchanged&quot;:false,&quot;currentTerminal&quot;:{&quot;terminalId&quot;:2,&quot;currentWorkingDirectory&quot;:&quot;E:/HXL_Book/jrc-service&quot;}}}],&quot;timestamp&quot;:&quot;2025-07-21T10:53:19.953Z&quot;,&quot;response_text&quot;:&quot;`lean()` 是 Mongoose 的一个重要方法，让我解释一下它的含义和作用：\n\n## lean() 的含义\n\n`lean()` 方法告诉 Mongoose 返回**纯 JavaScript 对象**，而不是 Mongoose 文档实例。\n\n### 1. 默认情况（不使用 lean）\n\n````javascript path=Routes/Borrow.js mode=EXCERPT\n// 返回 Mongoose 文档实例\nlet borrows = await Borrow.find(condition)\n// borrows[0] 是一个 Mongoose Document，包含很多方法\n````\n\n### 2. 使用 lean() 后\n\n````javascript path=Routes/Borrow.js mode=EXCERPT\n// 返回纯 JavaScript 对象\nlet borrows = await Borrow.find(condition).lean()\n// borrows[0] 是一个普通的 JS 对象，只包含数据\n````\n\n## 主要区别\n\n### 不使用 lean()：\n```javascript\nlet borrow = await Borrow.findOne({_id: \&quot;123\&quot;})\nconsole.log(borrow.constructor.name) // \&quot;Document\&quot;\n\n// 可以使用 Mongoose 方法\nawait borrow.save()\nawait borrow.populate('user_id')\nborrow.toJSON()\n```\n\n### 使用 lean()：\n```javascript\nlet borrow = await Borrow.findOne({_id: \&quot;123\&quot;}).lean()\nconsole.log(borrow.constructor.name) // \&quot;Object\&quot;\n\n// 这些方法不可用，因为它只是普通对象\n// borrow.save() // ❌ 错误！\n// borrow.populate() // ❌ 错误！\n```\n\n## lean() 的优势\n\n### 1. **性能更好**\n- 跳过 Mongoose 的文档实例化过程\n- 内存占用更少\n- 查询速度更快\n\n### 2. **数据更轻量**\n- 没有额外的 Mongoose 方法和属性\n- 序列化更快（如转 JSON）\n\n### 3. **适合只读操作**\n- 当你只需要读取数据，不需要修改时\n\n## 使用场景\n\n### ✅ 适合使用 lean() 的场景：\n```javascript\n// 1. 只是读取数据用于显示\nlet books = await Book.find({}).lean()\nres.json(books)\n\n// 2. 数据统计和分析\nlet stats = await Borrow.find({returned: false}).lean()\nlet count = stats.length\n\n// 3. 大量数据查询（性能优化）\nlet allBorrows = await Borrow.find({}).lean()\n```\n\n### ❌ 不适合使用 lean() 的场景：\n```javascript\n// 1. 需要修改数据\nlet borrow = await Borrow.findOne({_id: id}) // 不用 lean\nborrow.returned = true\nawait borrow.save()\n\n// 2. 需要使用 populate 等 Mongoose 功能\nlet borrow = await Borrow.findOne({_id: id}).populate('user_id') // 不用 lean\n\n// 3. 需要数据验证和中间件\nlet borrow = new Borrow(data) // 不用 lean\nawait borrow.save() // 会触发验证和中间件\n```\n\n## 在你的项目中\n\n从你选中的代码来看：\n```javascript\nlet borrows = await Borrow.find(condition).lean()\n```\n\n这里使用 `lean()` 很合适，因为看起来是在获取借阅记录用于展示或统计，不需要修改这些数据，所以使用 `lean()` 可以提高性能。\n\n**总结**：`lean()` = \&quot;我只要数据，不要 Mongoose 的额外功能\&quot;，换取更好的性能。\n&quot;,&quot;workspace_file_chunks&quot;:[]}],&quot;feedbackStates&quot;:{&quot;temp-fe-e9ca01f7-f14d-4ebc-a33b-c0efe712bcd8&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-359cd88c-386a-42e1-a624-16a2a5c1868e&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-35278d8d-360a-4e29-b0b8-c446f9ba8ff4&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-5be58e6c-cfb3-4d77-8c15-82e76014831a&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;}},&quot;toolUseStates&quot;:{&quot;c38b966e-a501-49fe-b33a-5720daf8c368;toolu_013uJxtPuEcR1EiHfVqAPsrE&quot;:{&quot;requestId&quot;:&quot;c38b966e-a501-49fe-b33a-5720daf8c368&quot;,&quot;toolUseId&quot;:&quot;toolu_013uJxtPuEcR1EiHfVqAPsrE&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;The following code sections were retrieved:\nPath: Model/User.js\n     1\tconst mongoose = require(\&quot;mongoose\&quot;);\n     2\tconst Schema = mongoose.Schema;\n     3\t\n     4\tconst User = new Schema({\n     5\t\temail: {\n     6\t\t\trequired: false,\n     7\t\t\ttype: String,\n     8\t\t},\n     9\t\tphone_number: {\n    10\t\t\trequired: false,\n    11\t\t\ttype: String,\n    12\t\t},\n    13\t\tname: {\n    14\t\t\trequired: false,\n    15\t\t\ttype: String,\n    16\t\t},\n    17\t\tpassword: {\n    18\t\t\trequired: false,\n    19\t\t\ttype: String,\n    20\t\t},\n    21\t\tis_active: {\n    22\t\t\trequired: true,\n    23\t\t\ttype: Boolean,\n    24\t\t},\n    25\t\tpatronid : {\n    26\t\t\trequired : true,\n    27\t\t\ttype: String,\n    28\t\t},\n    29\t\tlogin_date : {\n    30\t\t\trequired : true,\n    31\t\t\ttype: Date,\n    32\t\t},\n    33\t\tlanguage:{\n    34\t\t\trequired : false,\n    35\t\t\ttype: String,\n    36\t\t\tdefault:'en'\n    37\t\t},\n    38\t\toffset : {\n    39\t\t\trequired : false,\n    40\t\t\ttype: String,\n    41\t\t\tdefault:'+00'\n    42\t\t},\n    43\t});\n    44\t\n    45\tmodule.exports = mongoose.model(\&quot;User\&quot;, User);\n...\nPath: Model/Admin.js\n     1\tconst mongoose = require(\&quot;mongoose\&quot;);\n     2\tconst Schema = mongoose.Schema;\n     3\tconst Admin = new Schema({\n     4\t\tname: {\n     5\t\t\ttype: String,\n     6\t\t\trequired: true,\n     7\t\t},\n     8\t\temail: {\n     9\t\t\ttype: String,\n    10\t\t\trequired: true,\n    11\t\t},\n    12\t\tphone_number: {\n    13\t\t\ttype: String,\n    14\t\t\trequired: true,\n    15\t\t},\n    16\t\tpassword: {\n    17\t\t\ttype: String,\n    18\t\t\trequired: true,\n    19\t\t},\n    20\t\tis_active: {\n    21\t\t\ttype: Boolean,\n    22\t\t\trequired: true,\n    23\t\t},\n    24\t\tsuper_admin: {\n    25\t\t\ttype: Boolean,\n    26\t\t\tdefault: false,\n    27\t\t},\n    28\t\treset_password_token: {\n    29\t\t\ttype: String\n    30\t\t},\n    31\t\treset_password_expires: {\n    32\t\t\ttype: String\n    33\t\t},\n    34\t\trole: {\n    35\t\t\ttype: String,\n    36\t\t\trequired: true,\n    37\t\t\tdefault: \&quot;2\&quot;,\n    38\t\t},\n    39\t\tcollection_type: {\n    40\t\t\ttype: String,\n    41\t\t\trequired: false,\n    42\t\t\tdefault:'JYG'\n    43\t\t},\n    44\t});\n    45\tmodule.exports = mongoose.model(\&quot;Admin\&quot;, Admin);...\nPath: Model/Book.js\n...\n     3\t\n     4\tconst Book = new Schema({\n     5\t\tcategory_id: [\n     6\t\t\t{\n     7\t\t\t\ttype: Schema.Types.ObjectId,\n     8\t\t\t\tref: \&quot;Category\&quot;,\n     9\t\t\t},\n    10\t\t],\n    11\t\ttitle: {\n    12\t\t\trequired: true,\n    13\t\t\ttype: String,\n    14\t\t},\n    15\t\texcerpt: {\n    16\t\t\trequired: true,\n    17\t\t\ttype: String,\n    18\t\t},\n    19\t\tstock_quantity: {\n    20\t\t\trequired: true,\n    21\t\t\ttype: Number,\n    22\t\t},\n    23\t\tavailable_quantity: {\n    24\t\t\ttype: Number,\n    25\t\t},\n    26\t\tpublishingGroup: {\n    27\t\t\ttype: String,\n    28\t\t},\n    29\t\timprints: {\n    30\t\t\ttype: String,\n    31\t\t},\n    32\t\tauthor: {\n    33\t\t\trequired: true,\n    34\t\t\ttype: String,\n    35\t\t},\n    36\t\ttotal_pages: {\n    37\t\t\trequired: false,\n    38\t\t\ttype: Number,\n    39\t\t},\n    40\t\tcost: {\n    41\t\t\trequired: false,\n    42\t\t\ttype: String,\n    43\t\t},\n    44\t\tcover_photo: {\n    45\t\t\trequired: true,\n    46\t\t\ttype: String,\n    47\t\t},\n    48\t\tbook_pdf: {\n    49\t\t\trequired: true,\n    50\t\t\ttype: String,\n    51\t\t},\n    52\t\tbook_recomm : {\n    53\t\t\trequired: false,\n    54\t\t\ttype:Boolean,\n    55\t\t\tdefault: false,\n    56\t\t},\n    57\t\tbook_recomm_datetime : {\n    58\t\t\ttype: Date,\n    59\t\t\trequired: false\n    60\t\t},\n    61\t\tpreview_book: {\n    62\t\t\trequired: false,\n    63\t\t\ttype: String,\n    64\t\t},\n    65\t\tadded_by: {\n    66\t\t\ttype: Schema.Types.ObjectId,\n    67\t\t\tref: \&quot;Admin\&quot;,\n    68\t\t\trequired: true,\n    69\t\t},\n    70\t\tadded_at: {\n    71\t\t\ttype: Date,\n    72\t\t\trequired: true,\n    73\t\t},\n    74\t\tpublish_date :{\n    75\t\t\ttype: Date,\n    76\t\t\trequired: false,\n    77\t\t},\n    78\t\tisbn_no: {\n    79\t\t\ttype: String,\n    80\t\t\trequired: true\n    81\t\t},\n    82\t\tis_deleted: {\n    83\t\t\trequired: false,\n    84\t\t\ttype:Boolean,\n    85\t\t\tdefault: false,\n    86\t\t},\n    87\t\tdeleted_at: {\n    88\t\t\trequired: false,\n    89\t\t\ttype: Date,\n    90\t\t},\n    91\t    collection_type: {\n    92\t\t\ttype: String,\n    93\t\t\trequired: false,\n    94\t\t\tdefault:'JYG'\n    95\t\t},\n    96\t});\n    97\tmodule.exports = mongoose.model(\&quot;Book\&quot;, Book);...\nPath: Model/Reading.js\n     1\tconst mongoose = require(\&quot;mongoose\&quot;);\n     2\tconst Schema = mongoose.Schema;\n     3\tconst Reading = new Schema({\n     4\t    book_id: {\n     5\t        type: Schema.Types.ObjectId,\n     6\t        ref: \&quot;Book\&quot;,\n     7\t        required: true,\n     8\t    },\n     9\t\tip: {\n    10\t\t\ttype: String,\n    11\t\t\trequired: true,\n    12\t\t},\n    13\t    client_id: {\n    14\t        type: String,\n    15\t\t\trequired: true,   \t\n    16\t    },\n    17\t    read_date: {\n    18\t        type: Date,\n    19\t        required: true,\n    20\t    },\n    21\t    return_date: {\n    22\t        type: Date,\n    23\t        required: true,\n    24\t    },\n    25\t    returned: {\n    26\t        type: Boolean,\n    27\t        default: false,\n    28\t    },\n    29\t});\n    30\tmodule.exports = mongoose.model(\&quot;Reading\&quot;, Reading);...\nPath: Model/Reserve.js\n     1\tconst mongoose = require(\&quot;mongoose\&quot;);\n     2\tconst Schema = mongoose.Schema;\n     3\tconst Reserve = new Schema({\n     4\t\tuser_id: {\n     5\t        type: Schema.Types.ObjectId,\n     6\t        ref: \&quot;User\&quot;,\n     7\t        required: true,\n     8\t    },\n     9\t    book_id: {\n    10\t        type: Schema.Types.ObjectId,\n    11\t        ref: \&quot;Book\&quot;,\n    12\t        required: true,\n    13\t    },\n    14\t\temail: {\n    15\t\t\ttype: String,\n    16\t\t\trequired: true,\n    17\t\t},\n    18\t\treserve_date: {\n    19\t\t\ttype: Date,\n    20\t\t\trequired: true,\n    21\t\t},\n    22\t\tstatus: {\n    23\t\t\ttype: Boolean,\n    24\t\t\trequired: true,\n    25\t\t},\n    26\t\tis_deleted: {\n    27\t\t\ttype: Boolean,\n    28\t\t\tdefault: false,\n    29\t\t},\n    30\t\tis_mailed:{\n    31\t\t\ttype: Boolean,\n    32\t\t\tdefault: false,\n    33\t\t},\n    34\t\tis_ignored:{\n    35\t\t\ttype: Boolean,\n    36\t\t\tDefault: false,\n    37\t\t},\n    38\t\tmail_date: {\n    39\t\t\ttype: Date,\n    40\t\t\trequired: false,\n    41\t\t},\n    42\t\tis_blocked: {\n    43\t\t\ttype: Boolean,\n    44\t\t\trequired: false,\n    45\t\t\tdefault: false\n    46\t\t}\n    47\t});\n    48\tmodule.exports = mongoose.model(\&quot;Reserve\&quot;, Reserve);...\nPath: Routes/Book.js\n     1\tconst app = require(\&quot;express\&quot;).Router();\n     2\tvar multer = require(\&quot;multer\&quot;);\n     3\tconst Admin = require(\&quot;../Model/Admin\&quot;);\n     4\tconst { AdminHelper } = require(\&quot;../Helper/AdminHelper\&quot;);\n     5\tconst { UserHelper } = require(\&quot;../Helper/UserHelper\&quot;);\n     6\tconst Book = require(\&quot;../Model/Book\&quot;);\n     7\tconst Banner = require(\&quot;../Model/Banner\&quot;);\n     8\tconst Category = require(\&quot;../Model/Category\&quot;);\n     9\tconst fs = require(\&quot;fs\&quot;);\n    10\tconst AdminSession = require(\&quot;../Model/AdminSession\&quot;);\n    11\tconst UserSession = require(\&quot;../Model/UserSession\&quot;);\n    12\tconst Borrow = require(\&quot;../Model/Borrow\&quot;);\n    13\tconst Reading = require(\&quot;../Model/Reading\&quot;);\n    14\tconst Reserve = require(\&quot;../Model/Reserve\&quot;);\n    15\tconst Favourite = require(\&quot;../Model/Favourite\&quot;);\n    16\tconst Preview = require(\&quot;../Model/Preview\&quot;);\n    17\t// const cron = require('node-cron');\n...\n  1594\t\n  1595\t\t\tBook.findOne({\n  1596\t\t\t\t_id: mongoose.Types.ObjectId(req.body.id),\n  1597\t\t\t\t$or: [{ is_deleted: { $exists: false } }, { is_deleted: { $exists: true, $eq: false } }]\n  1598\t\t\t})\n  1599\t\t\t\t.populate(\&quot;category_id\&quot;)\n  1600\t\t\t\t.then(async (resx) =&gt; {\n  1601\t\t\t\t\tif (resx.length === 0) {\n  1602\t\t\t\t\t\tres.json({\n  1603\t\t\t\t\t\t\tcode: 422,\n  1604\t\t\t\t\t\t\tmessage: \&quot;Provided invalid id\&quot;,\n  1605\t\t\t\t\t\t\tstatus: false,\n  1606\t\t\t\t\t\t});\n  1607\t\t\t\t\t\treturn res.end();\n  1608\t\t\t\t\t}\n  1609\t\n  1610\t\t\t\t\tvar category_id = resx.category_id;\n  1611\t\t\t\t\tvar catResp = [];\n  1612\t\t\t\t\tcategory_id.forEach(function (el) {\n  1613\t\t\t\t\t\tvar cat_id = el._id;\n  1614\t\t\t\t\t\tBook.findOne({\n  1615\t\t\t\t\t\t\tcategory_id: {\n  1616\t\t\t\t\t\t\t\t$in: cat_id\n  1617\t\t\t\t\t\t\t},\n  1618\t\t\t\t\t\t\t_id: {\n  1619\t\t\t\t\t\t\t\t$ne: req.body.id\n  1620\t\t\t\t\t\t\t}\n  1621\t\t\t\t\t\t}).then(async (catRes) =&gt; {\n  1622\t\t\t\t\t\t\tif (catRes &amp;&amp; catRes != null) {\n  1623\t\t\t\t\t\t\t\tcatResp.push(catRes);\n  1624\t\t\t\t\t\t\t}\n  1625\t\t\t\t\t\t});\n  1626\t\t\t\t\t});\n  1627\t\n  1628\t\n  1629\t\t\t\t\tres.json({\n  1630\t\t\t\t\t\tcode: 200,\n  1631\t\t\t\t\t\tdata: {\n  1632\t\t\t\t\t\t\tbook: resx,\n  1633\t\t\t\t\t\t\tcatResp: catResp,\n  1634\t\t\t\t\t\t\tborrowed: true\n  1635\t\t\t\t\t\t},\n  1636\t\t\t\t\t\tmessage: \&quot;Operation successful.\&quot;,\n  1637\t\t\t\t\t});\n  1638\t\t\t\t\treturn res.end();\n  1639\t\t\t\t});\n  1640\t\t}\n  1641\t\n  1642\t});\n...\n  2058\t\tif (req.body.category &amp;&amp; req.body.is_available &amp;&amp; req.body.query) {\n  2059\t\t\tif (req.body.queryType == '1') {\n  2060\t\t\t\tlet match = {\n  2061\t\t\t\t\ttitle: {\n  2062\t\t\t\t\t\t$regex: req.body.query,\n  2063\t\t\t\t\t\t$options: \&quot;i\&quot;\n  2064\t\t\t\t\t},\n  2065\t\t\t\t\tcategory_id: mongoose.Types.ObjectId(req.body.category),\n  2066\t\t\t\t\tavailable_quantity: { $gt: 0 }\n  2067\t\t\t\t};\n  2068\t\t\t\tawait filterRecords(res, match, sortBy, sortByType, collection_type);\n  2069\t\t\t}\n  2070\t\t\tif (req.body.queryType == '2') {\n  2071\t\t\t\tlet match = {\n  2072\t\t\t\t\tauthor: {\n  2073\t\t\t\t\t\t$regex: req.body.query,\n  2074\t\t\t\t\t\t$options: \&quot;i\&quot;\n  2075\t\t\t\t\t},\n  2076\t\t\t\t\tcategory_id: mongoose.Types.ObjectId(req.body.category),\n  2077\t\t\t\t\tavailable_quantity: { $gt: 0 }\n  2078\t\t\t\t};\n  2079\t\t\t\tawait filterRecords(res, match, sortBy, sortByType, collection_type);\n  2080\t\t\t}\n...\n  2219\t\n  2220\t\n  2221\t\tif (sortByType) { if (sortByType == \&quot;asc\&quot;) { sortByType = 1; } if (sortByType == \&quot;desc\&quot;) { sortByType = -1; } }\n  2222\t\tvar reslt = await Book.find(_match, { title: 1, author: 1, publish_date: 1, cover_photo: 1 }).sort([[sortbycol, sortByType]]).exec(function (err, result) {\n  2223\t\t\tif (err) {\n  2224\t\t\t\tres.json({\n  2225\t\t\t\t\tcode: 500,\n  2226\t\t\t\t\tmessage: \&quot;Internal error in query book\&quot;,\n  2227\t\t\t\t\tsuccess: false,\n  2228\t\t\t\t});\n  2229\t\t\t\treturn res.end();\n  2230\t\t\t}\n  2231\t\n  2232\t\t\tif (!result) {\n  2233\t\t\t\tres.json({\n  2234\t\t\t\t\tcode: 422,\n  2235\t\t\t\t\tmessage: \&quot;Operation failed\&quot;,\n  2236\t\t\t\t\tsuccess: false,\n  2237\t\t\t\t});\n  2238\t\t\t\treturn res.end();\n  2239\t\t\t}\n  2240\t\n  2241\t\t\tres.json({\n  2242\t\t\t\tcode: 200,\n  2243\t\t\t\tdata: result,\n  2244\t\t\t\tmessage: \&quot;Operation successful.\&quot;\n  2245\t\t\t});\n  2246\t\t\tres.end();\n  2247\t\t});\n...\nPath: Model/Borrow.js\n     1\tconst mongoose = require(\&quot;mongoose\&quot;);\n     2\tconst Schema = mongoose.Schema;\n     3\t\n     4\tconst Borrow = new Schema({\n     5\t    user_id: {\n     6\t        type: Schema.Types.ObjectId,\n     7\t        ref: \&quot;User\&quot;,\n     8\t        required: true,\n     9\t    },\n    10\t    book_id: {\n    11\t        type: Schema.Types.ObjectId,\n    12\t        ref: \&quot;Book\&quot;,\n    13\t        required: true,\n    14\t    },\n    15\t    email: {\n    16\t        type: String,\n    17\t    },\n    18\t    issue_date: {\n    19\t        type: Date,\n    20\t        required: true,\n    21\t    },\n    22\t    return_date: {\n    23\t        type: Date,\n    24\t        required: true,\n    25\t    },\n    26\t    returned: {\n    27\t        type: Boolean,\n    28\t        default: false,\n    29\t    },\n    30\t    reborrowed_once: {\n    31\t        type: Boolean,\n    32\t        default: false\n    33\t    },\n    34\t    reborrow_one_date: {\n    35\t        type: Date\n    36\t    },\n    37\t    reborrowed_twice: {\n    38\t        type: Boolean,\n    39\t        default: false\n    40\t    },\n    41\t    is_deleted: {\n    42\t        type: Boolean,\n    43\t        default: false\n    44\t    },\n    45\t    reborrow_two_date: {\n    46\t        type: Date\n    47\t    }\n    48\t});\n    49\t\n    50\tmodule.exports = mongoose.model(\&quot;Borrow\&quot;, Borrow);\n...\nPath: Model/Banner.js\n     1\tconst mongoose = require(\&quot;mongoose\&quot;);\n     2\tconst Schema = mongoose.Schema;\n     3\t\n     4\tconst Banner = new Schema({\n     5\t    bannerimage: {\n     6\t        required: true,\n     7\t        type: String,\n     8\t    },\n     9\t    url: {\n    10\t        required: true,\n    11\t        type: String,\n    12\t    },\n    13\t    collection_type: {\n    14\t\t\ttype: String,\n    15\t\t\trequired: false,\n    16\t\t\tdefault:'JYG'\n    17\t\t},\n    18\t}, {timestamps: true});\n    19\t\n    20\tmodule.exports = mongoose.model(\&quot;banner\&quot;, Banner);\n...\nPath: Model/UserSession.js\n     1\tconst mongoose = require(\&quot;mongoose\&quot;);\n     2\tconst Schema = mongoose.Schema;\n     3\t\n     4\tconst UserSession = new Schema({\n     5\t\tuser_id: {\n     6\t\t\trequired: true,\n     7\t\t\ttype: Schema.Types.ObjectId,\n     8\t\t\tref: \&quot;User\&quot;,\n     9\t\t},\n    10\t\ttoken: {\n    11\t\t\trequired: true,\n    12\t\t\ttype: String,\n    13\t\t},\n    14\t\tis_active: {\n    15\t\t\trequired: true,\n    16\t\t\ttype: Boolean,\n    17\t\t},\n    18\t\tip: {\n    19\t\t\trequired: false,\n    20\t\t\ttype: String,\n    21\t\t},\n    22\t\tuser_agent: {\n    23\t\t\trequired: true,\n    24\t\t\ttype: String,\n    25\t\t},\n    26\t\tdevice: {\n    27\t\t\trequired: true,\n    28\t\t\ttype: String,\n    29\t\t},\n    30\t\tdate:{\n    31\t\t\trequired : true,\n    32\t\t\ttype: Date,\n    33\t\t},\n    34\t\tforceLogoutAt:{\n    35\t\t\trequired : false,\n    36\t\t\ttype: Date,\n    37\t\t},\n    38\t\tlogoutAt:{\n    39\t\t\trequired : false,\n    40\t\t\ttype: Date,\n    41\t\t},\n    42\t\ttimestamp: { type: Date, default: Date.now}\n    43\t});\n    44\t\n    45\tmodule.exports = mongoose.model(\&quot;UserSession\&quot;, UserSession);...\nPath: Helper/BookHelper.js\n     1\tconst Borrow = require(\&quot;../Model/Borrow\&quot;);\n     2\tconst Favourite = require(\&quot;../Model/Favourite\&quot;);\n     3\tconst moment = require(\&quot;moment\&quot;);\n     4\tconst Book = require(\&quot;../Model/Book\&quot;);\n     5\tconst Preview = require(\&quot;../Model/Preview\&quot;);\n     6\tconst Reserve = require(\&quot;../Model/Reserve\&quot;);\n     7\tconst Admin = require(\&quot;../Model/Admin\&quot;);\n     8\tconst Category = require(\&quot;../Model/Category\&quot;);\n     9\tvar mongoose = require('mongoose');\n    10\tconst async = require(\&quot;async\&quot;);\n    11\tconst R = require('ramda')\n    12\tasync function isBookBorrowed(userId, bookId) {\n    13\t  const borrows = await Borrow.find({\n    14\t    user_id: mongoose.Types.ObjectId(userId),\n    15\t    book_id: mongoose.Types.ObjectId(bookId),\n    16\t    returned: false,\n    17\t  });\n...\n   179\t\n   180\tasync function updateAvailableStock(bookId, type) {\n   181\t  const books = await Book.findOne({\n   182\t    _id: mongoose.Types.ObjectId(bookId)\n   183\t  });\n   184\t\n   185\t  if (!books) {\n   186\t    console.log(`Book not found: ${bookId}`);\n   187\t    return;\n   188\t  }\n   189\t\n   190\t  const originalQuantity = books.available_quantity;\n   191\t  console.log(`updateAvailableStock - Book: ${bookId}, Type: ${type}, Current stock: ${originalQuantity}/${books.stock_quantity}`);\n   192\t\n   193\t  // If available_quantity column is not exist in table then it will update this\n   194\t  if (type == \&quot;borrow\&quot;) {\n   195\t    var available_quantity = parseInt(books.available_quantity) - 1;\n   196\t    await Book.findOneAndUpdate({\n   197\t      _id: mongoose.Types.ObjectId(bookId)\n   198\t    }, {\n   199\t      available_quantity: available_quantity\n   200\t    });\n   201\t    console.log(`Stock decreased for borrow: ${originalQuantity} -&gt; ${available_quantity}`);\n   202\t  }\n...\n   224\t\n   225\tasync function renewBooksbydateV2(startDate, endDate, email, collection_type) {\n   226\t  //  const _startDate = moment(startDate)\n   227\t  //    .tz(\&quot;Asia/Hong_Kong\&quot;)\n   228\t  //    .startOf(\&quot;d\&quot;)\n   229\t  //    .toDate();\n   230\t  //  const _endDate = moment(endDate).tz(\&quot;Asia/Hong_Kong\&quot;).endOf(\&quot;d\&quot;).toDate();\n   231\t\n   232\t  const admin = await Admin.findOne({\n   233\t    email,\n   234\t  });\n   235\t\n   236\t  const firstMatchCondition = {\n   237\t    \&quot;books.collection_type\&quot;: collection_type,\n   238\t    reborrowed_once: true,\n   239\t    reborrow_one_date: {\n   240\t      $gte: new Date(startDate),\n   241\t      $lte: new Date(endDate),\n   242\t    },\n   243\t  };\n   244\t\n   245\t  const secondMatchCondition = {\n   246\t    \&quot;books.collection_type\&quot;: collection_type,\n   247\t    reborrowed_twice: true,\n   248\t    reborrow_two_date: {\n   249\t      $gte: new Date(startDate),\n   250\t      $lte: new Date(endDate),\n   251\t    },\n   252\t  };\n...\n   937\t\n   938\t  return recommendedBooks;\n   939\t}\n   940\t\n   941\tasync function getunusedBookByDateV2(\n   942\t  collection_type,\n   943\t  startDate,\n   944\t  endDate,\n   945\t  email) {\n   946\t  const _startDate = moment(startDate)\n   947\t    .tz(\&quot;Asia/Hong_Kong\&quot;)\n   948\t    .startOf(\&quot;d\&quot;)\n   949\t    .toDate();\n   950\t  const _endDate = moment(endDate)\n   951\t    .tz(\&quot;Asia/Hong_Kong\&quot;)\n   952\t    .endOf(\&quot;d\&quot;)\n   953\t    .toDate();\n   954\t\n   955\t  const splitTimePoint = new Date(\&quot;2022-11-24T16:00:00.000Z\&quot;);\n   956\t\n   957\t  const admin = await Admin.findOne({ email });\n   958\t\n   959\t  // reserve_date;\n   960\t\n   961\t  // issue_date;\n   962\t\n   963\t  const borrowedBooks = await Borrow.distinct(\&quot;book_id\&quot;, {\n   964\t    issue_date: {\n   965\t      $gte: _startDate,\n   966\t      $lte: _endDate,\n   967\t    },\n   968\t  });\n   969\t  const reservedBooks = await Reserve.distinct(\&quot;book_id\&quot;, {\n   970\t    reserve_date: {\n   971\t      $gte: _startDate,\n   972\t      $lte: _endDate,\n   973\t    },\n   974\t  });\n...\n  1295\t\n  1296\t/** 05-01-2022 -&gt; Optimised **/\n  1297\tasync function recommendedBooksbydate(book_id, startDate, endDate, all, email, isPetronVisible, collection_type) {\n  1298\t  let resx;\n  1299\t  startDate = new Date(startDate);\n  1300\t  endDate = new Date(endDate);\n  1301\t  //await (Admin.findOne({email: email}, function(err, res){ resx = (res); }).select({ _id: 1, role: 1 }).lean().exec());\n  1302\t  let catArray = await findCats();\n...\n  1733\t\n  1734\t/** 18-01-2022 -&gt; Optimised **/\n  1735\tasync function AllBooksbydate(book_id, startDate, endDate, all, email, collection_type) {\n  1736\t  let catArray = await findCats();\n  1737\t  var sortedBooks = [];\n  1738\t  var result = await Book.aggregate([{\n  1739\t    $match: {\n  1740\t      _id: {\n  1741\t        $ne: book_id\n  1742\t      },\n  1743\t      $or: [{ is_deleted: { $exists: false } }, { is_deleted: { $exists: true, $eq: false } }],\n  1744\t      added_at: {\n  1745\t        \&quot;$lte\&quot;: new Date(endDate)\n  1746\t      },\n  1747\t      collection_type\n  1748\t    }\n  1749\t  },\n  1750\t  {\n  1751\t    $group: {\n  1752\t      \&quot;_id\&quot;: {\n  1753\t        \&quot;book_id\&quot;: \&quot;$_id\&quot;,\n  1754\t        \&quot;title\&quot;: \&quot;$title\&quot;,\n  1755\t        \&quot;isbn_no\&quot;: \&quot;$isbn_no\&quot;,\n  1756\t        \&quot;author\&quot;: \&quot;$author\&quot;,\n  1757\t        \&quot;available_quantity\&quot;: \&quot;$available_quantity\&quot;,\n  1758\t        \&quot;stock_quantity\&quot;: \&quot;$stock_quantity\&quot;,\n  1759\t        \&quot;added_by\&quot;: \&quot;$added_by\&quot;,\n  1760\t        \&quot;imprints\&quot;: \&quot;$imprints\&quot;,\n  1761\t        \&quot;publishingGroup\&quot;: \&quot;$publishingGroup\&quot;,\n  1762\t        \&quot;category_id\&quot;: \&quot;$category_id\&quot;,\n  1763\t        \&quot;book_recomm\&quot;: \&quot;$book_recomm\&quot;\n  1764\t      },\n  1765\t      count: {\n  1766\t        $sum: 1\n  1767\t      }\n  1768\t    }\n  1769\t  },\n...\nPath: Routes/User.js\n...\n   188\t\n   189\tapp.post(\&quot;/getUserDetail\&quot;, upload.none(), async (req, res) =&gt; {\n   190\t\tif (req.body.user_id) {\n   191\t\t\tawait User.findOne({ _id: req.body.user_id }).then((foundData) =&gt; {\n   192\t\t\t\tres.json({\n   193\t\t\t\t\tcode: 200,\n   194\t\t\t\t\tmessage: \&quot;Operation Successful\&quot;,\n   195\t\t\t\t\tdata: {\n   196\t\t\t\t\t\tuser_id: foundData._id,\n   197\t\t\t\t\t\temail: foundData.email,\n   198\t\t\t\t\t\tphone_number: foundData.phone_number,\n   199\t\t\t\t\t\tname: foundData.name,\n   200\t\t\t\t\t}\n   201\t\t\t\t});\n   202\t\t\t\treturn res.end();\n   203\t\t\t});\n   204\t\t} else {\n   205\t\t\tres.json({\n   206\t\t\t\tcode: 422,\n   207\t\t\t\tmessage: \&quot;User ID in request\&quot;,\n   208\t\t\t\tsuccess: false,\n   209\t\t\t});\n   210\t\t\treturn res.end();\n   211\t\t}\n   212\t});\n   213\tapp.post(\&quot;/setUserDetail\&quot;, upload.none(), UserHelper, async (req, res) =&gt; {\n   214\t\tif (\n   215\t\t\t!req.body.email\n   216\t\t) {\n   217\t\t\tres.setHeader(\&quot;Content-Type\&quot;, \&quot;application/json\&quot;);\n   218\t\t\tres.status(422).json({\n   219\t\t\t\tcode: 422,\n   220\t\t\t\tmessage: \&quot;Missing Email-id\&quot;,\n   221\t\t\t\tsuccess: false,\n   222\t\t\t});\n   223\t\t\treturn res.end();\n   224\t\t}\n...\nPath: docker-entrypoint-initdb/mongo.init.js\n     1\tdb.createUser({\n     2\t  // user for database which shall be created\n     3\t  user: \&quot;service\&quot;,\n     4\t  // password of user\n     5\t  pwd: \&quot;b5f56b5df5ea1e5682dda6c1a283c91f\&quot;,\n     6\t  roles: [\n     7\t    {\n     8\t      role: \&quot;readWrite\&quot;,\n     9\t      db: \&quot;ebook\&quot;,\n    10\t    },\n    11\t  ],\n    12\t});\n...\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;90da819d-c247-47c8-89e4-eb1fff78057a;toolu_vrtx_016KqiWujfThyiePoLCJttzf&quot;:{&quot;requestId&quot;:&quot;90da819d-c247-47c8-89e4-eb1fff78057a&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_016KqiWujfThyiePoLCJttzf&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;The following code sections were retrieved:\nPath: Routes/Borrow.js\n...\n   116\t\n   117\t                const mBorrow = new Borrow({\n   118\t                    email: user.email,\n   119\t                    user_id: user._id,\n   120\t                    book_id: mongoose.Types.ObjectId(book_id),\n   121\t                    issue_date: issue_date,\n   122\t                    return_date: return_date,\n   123\t                    returned: false,\n   124\t                    is_deleted: false\n   125\t                });\n   126\t                await mBorrow.save();\n   127\t\n   128\t                await BookHelper.updateAvailableStock(book_id, 'borrow');\n   129\t                await sendBorrowMail(user.email, book.title, user.name, datetoshow);\n   130\t            }\n   131\t        }\n   132\t    }\n   133\t}\n   134\t\n   135\t/**\n   136\t * 发送借阅成功邮件\n   137\t */\n   138\tasync function sendBorrowMail(email, bookTitle, userName, returnDate) {\n   139\t    let mailOptions = {\n   140\t        to: email,\n   141\t        from: \&quot;<EMAIL>\&quot;,\n   142\t        subject: \&quot;金閱閣電子書預約書籍通知\&quot;,\n   143\t        html: `親愛的 ${userName} 讀者:&lt;br/&gt;&lt;br/&gt;\n   144\t        你在香港公共圖書館金閱閣電子書預約的《${bookTitle}》已借入你的帳戶內，期限至 ${returnDate}。\n   145\t        你可登入香港公共圖書館的 &lt;a href=\&quot;https://joyread.club\&quot;&gt;金閱閣電子書&lt;/a&gt;，透過瀏覽器線上閱讀。&lt;br/&gt;&lt;br/&gt;\n   146\t        多謝使用金閱閣電子書。`\n   147\t    };\n   148\t    await mailer(mailOptions);\n   149\t}\n...\n   212\t\n   213\tapp.post(\&quot;/book\&quot;, upload.none(), UserHelper, async (req, res) =&gt; {\n   214\t    if (\n   215\t        !req.body.user_id ||\n   216\t        !req.body.book_id ||\n   217\t        !req.body.issue_date ||\n   218\t        !req.body.return_date ||\n   219\t        !req.body.status\n   220\t    ) {\n   221\t        return res.json({\n   222\t            code: 418,\n   223\t            message: \&quot;Missing required fields\&quot;,\n   224\t            status: false,\n   225\t        });\n   226\t    }\n   227\t\n   228\t    // 同一用户同一本书只能借阅一次\n   229\t    var cacheKey = 'cache_' + req.body.user_id + '_' + req.body.book_id;\n   230\t    if (cache[cacheKey]) {\n   231\t        // 存在，表示处理中，直接返回\n   232\t        return res.json({\n   233\t            code: 418,\n   234\t            message: \&quot;Processing in progress\&quot;,\n   235\t            status: false,\n   236\t        });\n   237\t    }\n   238\t    cache[cacheKey] = cacheKey;\n   239\t\n   240\t    const _user = await User.findOne({\n   241\t        _id: new mongoose.Types.ObjectId(req.body.user_id)\n   242\t    })\n   243\t    if (!_user) {\n   244\t        const systemLog = SystemLog({\n   245\t            ctime: new Date(),\n   246\t            data: {\n   247\t                body: req.body,\n   248\t                token: req.header(\&quot;SESSION-TOKEN\&quot;)\n   249\t            }\n   250\t        })\n   251\t        await systemLog.save()\n   252\t    }\n...\n   322\t                } else {\n   323\t                    var user_id = req.body.user_id;\n   324\t                    var book_id = req.body.book_id;\n   325\t\n   326\t                    const mBorrow = Borrow({\n   327\t                        email: req.body.email,\n   328\t                        user_id: req.body.user_id,\n   329\t                        book_id: req.body.book_id,\n   330\t                        issue_date: issue_date,\n   331\t                        return_date: return_date,\n   332\t                        returned: req.body.status,\n   333\t                        is_deleted: false\n   334\t                    });\n   335\t\n   336\t                    mBorrow\n   337\t                        .save()\n   338\t                        .then((stored) =&gt; {\n   339\t                            (BookHelper.updateAvailableStock(book_id, 'borrow'));\n   340\t                            Reserve.findOneAndUpdate({\n   341\t                                book_id: req.body.book_id,\n   342\t                                is_deleted: false,\n   343\t                                user_id: req.body.user_id\n   344\t                            }, {\n   345\t                                is_deleted: true\n   346\t                            }, {\n   347\t                                useFindAndModify: false\n   348\t                            },\n   349\t                                (err, updated) =&gt; { });\n   350\t                            User.findOne({\n   351\t                                _id: user_id\n   352\t                            }).then((userData) =&gt; {\n   353\t                                if (userData) {\n   354\t                                    var email = req.body.email;\n   355\t                                    var name = userData.name;\n   356\t                                    Book.findOne({\n   357\t                                        _id: book_id\n   358\t                                    }).then((bookData) =&gt; {\n...\n   543\t\n   544\tapp.post(\&quot;/deleteBook\&quot;, upload.none(), UserHelper, async (req, res) =&gt; {\n   545\t    if (!req.body.delete_id) {\n   546\t        return res.json({\n   547\t            code: 422,\n   548\t            message: \&quot;Missing required fields\&quot;,\n   549\t            status: false,\n   550\t        });\n   551\t    }\n   552\t    let doc = await Borrow.findOneAndUpdate({\n   553\t        _id: req.body.delete_id\n   554\t    }, {\n   555\t        is_deleted: true\n   556\t    });\n   557\t\n   558\t    return res.json({\n   559\t        code: 200,\n   560\t        data: false,\n   561\t        message: \&quot;Operation successful.\&quot;,\n   562\t    });\n   563\t});\n...\nPath: Routes/Book.js\n     1\tconst app = require(\&quot;express\&quot;).Router();\n     2\tvar multer = require(\&quot;multer\&quot;);\n     3\tconst Admin = require(\&quot;../Model/Admin\&quot;);\n     4\tconst { AdminHelper } = require(\&quot;../Helper/AdminHelper\&quot;);\n     5\tconst { UserHelper } = require(\&quot;../Helper/UserHelper\&quot;);\n     6\tconst Book = require(\&quot;../Model/Book\&quot;);\n     7\tconst Banner = require(\&quot;../Model/Banner\&quot;);\n     8\tconst Category = require(\&quot;../Model/Category\&quot;);\n     9\tconst fs = require(\&quot;fs\&quot;);\n    10\tconst AdminSession = require(\&quot;../Model/AdminSession\&quot;);\n    11\tconst UserSession = require(\&quot;../Model/UserSession\&quot;);\n    12\tconst Borrow = require(\&quot;../Model/Borrow\&quot;);\n    13\tconst Reading = require(\&quot;../Model/Reading\&quot;);\n    14\tconst Reserve = require(\&quot;../Model/Reserve\&quot;);\n    15\tconst Favourite = require(\&quot;../Model/Favourite\&quot;);\n    16\tconst Preview = require(\&quot;../Model/Preview\&quot;);\n    17\t// const cron = require('node-cron');\n...\n   839\t\n   840\tapp.post(\&quot;/delete\&quot;, AdminHelper, upload.none(), async (req, res) =&gt; {\n   841\t\tif (!req.body.book_id) {\n   842\t\t\tres.json({\n   843\t\t\t\tcode: 422,\n   844\t\t\t\tmessage: \&quot;Missing fields in request\&quot;,\n   845\t\t\t\tsuccess: false,\n   846\t\t\t});\n   847\t\t\treturn res.end();\n   848\t\t}\n   849\t\n   850\t\tawait Book.findOneAndUpdate({\n   851\t\t\t_id: mongoose.Types.ObjectId(req.body.book_id)\n   852\t\t}, {\n   853\t\t\tis_deleted: true,\n   854\t\t\tdeleted_at: new Date()\n   855\t\t}, {\n   856\t\t\tuseFindAndModify: false\n   857\t\t},\n   858\t\t\tasync (err, updated) =&gt; {\n   859\t\t\t\tif (err) {\n   860\t\t\t\t\treturn res.json({\n   861\t\t\t\t\t\tcode: 500,\n   862\t\t\t\t\t\tmessage: \&quot;Internal error at updating book-cover\&quot;,\n   863\t\t\t\t\t\tsuccess: false,\n   864\t\t\t\t\t});\n   865\t\t\t\t}\n   866\t\n   867\t\t\t\tvar countsborrow = await Borrow.find({\n   868\t\t\t\t\tis_deleted: false,\n   869\t\t\t\t\treturned: false,\n   870\t\t\t\t\tbook_id: mongoose.Types.ObjectId(req.body.book_id)\n   871\t\t\t\t}).countDocuments();\n...\n  3364\t\t\t\t\t\tawait Book.findByIdAndUpdate(\n  3365\t\t\t\t\t\t\treq.body.id, {\n  3366\t\t\t\t\t\t\tavailable_quantity: newAvailableQuantity\n  3367\t\t\t\t\t\t}, {\n  3368\t\t\t\t\t\t\tuseFindAndModify: false\n  3369\t\t\t\t\t\t},\n  3370\t\t\t\t\t\t\t(err, updated) =&gt; {\n  3371\t\t\t\t\t\t\t\tif (err)\n  3372\t\t\t\t\t\t\t\t\tconsole.log(\&quot;Internal error in update qty.\&quot;);\n  3373\t\n  3374\t\t\t\t\t\t\t\tconsole.log(\&quot;Update qty successful.\&quot;);\n  3375\t\t\t\t\t\t\t}\n  3376\t\t\t\t\t\t);\n  3377\t\t\t\t\t}\n  3378\t\n  3379\t\t\t\t\tawait Book.findByIdAndUpdate(\n  3380\t\t\t\t\t\treq.body.id, {\n  3381\t\t\t\t\t\t[req.body.field]: req.body.value\n  3382\t\t\t\t\t}, {\n  3383\t\t\t\t\t\tuseFindAndModify: false\n  3384\t\t\t\t\t},\n  3385\t\t\t\t\t\t(err, updated) =&gt; {\n  3386\t\t\t\t\t\t\tif (err)\n  3387\t\t\t\t\t\t\t\treturn res.json({\n  3388\t\t\t\t\t\t\t\t\tcode: 500,\n  3389\t\t\t\t\t\t\t\t\tmessage: \&quot;Internal error.\&quot;,\n  3390\t\t\t\t\t\t\t\t\tsuccess: false,\n  3391\t\t\t\t\t\t\t\t});\n  3392\t\t\t\t\t\t\treturn res.json({\n  3393\t\t\t\t\t\t\t\tcode: 200,\n  3394\t\t\t\t\t\t\t\tdata: updated,\n  3395\t\t\t\t\t\t\t\tmessage: \&quot;Operation successful\&quot;,\n  3396\t\t\t\t\t\t\t});\n  3397\t\t\t\t\t\t}\n  3398\t\t\t\t\t);\n  3399\t\n  3400\t\t\t\t}\n  3401\t\t\t} catch (e) {\n  3402\t\t\t\tconsole.log(e.message);\n  3403\t\t\t\tres.json({\n  3404\t\t\t\t\tcode: 422,\n  3405\t\t\t\t\tmessage: e.message,\n  3406\t\t\t\t\tsuccess: false,\n  3407\t\t\t\t});\n  3408\t\t\t\treturn res.end();\n  3409\t\t\t}\n  3410\t\t}\n  3411\t);\n...\nPath: Routes/Category.js\n...\n    84\t\n    85\tapp.post(\&quot;/edit\&quot;, AdminHelper, upload.none(), async (req, res) =&gt; {\n    86\t\tif (!req.body.name || !req.body.category_id) {\n    87\t\t\tres.json({\n    88\t\t\t\tcode: 422,\n    89\t\t\t\tmessage: \&quot;Missing fields in request\&quot;,\n    90\t\t\t\tsuccess: false,\n    91\t\t\t});\n    92\t\t\treturn res.end();\n    93\t\t}\n    94\t\tawait Category.findOneAndUpdate(\n    95\t\t\t{ _id: req.body.category_id },\n    96\t\t\t{ name: req.body.name }\n    97\t\t).then((deletedOne) =&gt; {\n    98\t\t\treturn res.json({\n    99\t\t\t\tcode: 200,\n   100\t\t\t\tmessage: \&quot;Modification successful\&quot;,\n   101\t\t\t\tstatus: true,\n   102\t\t\t});\n   103\t\t});\n   104\t});\n   105\t\n   106\tapp.post(\&quot;/create\&quot;, upload.none(), AdminHelper, async (req, res) =&gt; {\n   107\t\tif (!req.body.name) {\n   108\t\t\tres.json({\n   109\t\t\t\tcode: 422,\n   110\t\t\t\tmessage: \&quot;Missing fields in request\&quot;,\n   111\t\t\t\tsuccess: false,\n   112\t\t\t});\n   113\t\t\treturn res.end();\n   114\t\t}\n   115\t\tUserSession.findOne;\n   116\t\tconst mCategory = new Category({\n   117\t\t\tname: req.body.name,\n   118\t\t});\n   119\t\tawait Category.findOne({ name: {'$regex': req.body.name, '$options': 'i'}} ).then(async(ress) =&gt; {\n   120\t\t\tif(!ress)\n   121\t\t\t{\n   122\t\t\t\tawait mCategory\n   123\t\t\t\t.save()\n   124\t\t\t\t.then((storedData) =&gt; {\n   125\t\t\t\t\tres.json({\n   126\t\t\t\t\t\tcode: 200,\n   127\t\t\t\t\t\tmessage: \&quot;Create operation successful\&quot;,\n   128\t\t\t\t\t\tdata: {\n   129\t\t\t\t\t\t\tid: storedData._id,\n   130\t\t\t\t\t\t\tname: storedData.name,\n   131\t\t\t\t\t\t},\n   132\t\t\t\t\t});\n   133\t\t\t\t\tres.end();\n   134\t\t\t\t})\n   135\t\t\t\t.catch((err) =&gt; {\n   136\t\t\t\t\tres.json({\n   137\t\t\t\t\t\tmessage: \&quot;Internal error in creating category.\&quot;,\n   138\t\t\t\t\t\tsuccess: false,\n   139\t\t\t\t\t\tcode: 500,\n   140\t\t\t\t\t});\n   141\t\t\t\t\tres.end();\n   142\t\t\t\t});\n   143\t\t\t}\n   144\t\t\telse{\n   145\t\t\t\t\n   146\t\t\t\t\tres.json({\n   147\t\t\t\t\t\tmessage: \&quot;Category Already Exist !\&quot;,\n   148\t\t\t\t\t\tsuccess: false,\n   149\t\t\t\t\t\tcode: 500,\n   150\t\t\t\t\t});\n   151\t\t\t\t\tres.end();\n   152\t\t\t\t\n   153\t\t\t}\n   154\t\t});\n   155\t\t\n   156\t});\n   157\tmodule.exports = {\n   158\t    router: app\n   159\t};\n...\nPath: Routes/Reading.js\n...\n    48\t\n    49\t    if (existingReading) {\n    50\t        console.log(`Client ${req.body.client_id} already has active reading for book ${book_id}, reading_id: ${existingReading._id}`);\n    51\t        res.json({\n    52\t            code: 200,\n    53\t            message: \&quot;Book start reading\&quot;,\n    54\t            data: {\n    55\t                reading_id: existingReading._id,\n    56\t            },\n    57\t        });\n    58\t\n    59\t        return res.end();\n    60\t    }\n    61\t    books_stock = await BookHelper.isBookInStock(book_id);\n    62\t    if (books_stock &lt;= 0) {\n    63\t        res.json({\n    64\t            code: 401,\n    65\t            message: \&quot;Book not has stock\&quot;,\n    66\t            status: false,\n    67\t        });\n    68\t        return res.end();\n    69\t    }; \n    70\t\n    71\t    const newReading = new Reading({\n    72\t        book_id: book_id,\n    73\t        client_id: req.body.client_id,\n    74\t        ip: ip,\n    75\t        read_date: new Date(),\n    76\t        return_date: new Date(),\n    77\t        returned: false,\n    78\t    });\n    79\t    await newReading.save();\n    80\t\n    81\t    const newReadingRecord = new ReadingRecord({\n    82\t        reading_id: newReading._id,\n    83\t        read_time: new Date(),\n    84\t    });\n    85\t    await newReadingRecord.save();\n    86\t\n    87\t    console.log(`New reading started - Client: ${req.body.client_id}, Book: ${book_id}, Reading ID: ${newReading._id}, IP: ${ip}`);\n    88\t    console.log(\&quot;record id is:\&quot;,newReadingRecord._id);\n    89\t\n    90\t    // 更新库存前记录当前库存状态\n    91\t    const bookBefore = await BookHelper.isBookInStock(book_id);\n    92\t    await BookHelper.updateAvailableStock(book_id, 'reading');\n    93\t    const bookAfter = await BookHelper.isBookInStock(book_id);\n    94\t    console.log(`Stock updated for book ${book_id}: ${bookBefore} -&gt; ${bookAfter}`);\n    95\t\n    96\t    res.json({\n    97\t        code: 200,\n    98\t        message: \&quot;Book start reading\&quot;,\n    99\t        data: {\n   100\t            reading_id: newReading._id,\n   101\t        },\n   102\t    });\n   103\t\n   104\t    return res.end();\n   105\t\n   106\t});\n   107\t\n   108\tapp.post(\&quot;/record\&quot;, upload.none(), async (req, res) =&gt; {\n   109\t    if (!req.body.reading_id) {\n   110\t        return res.json({\n   111\t            code: 422,\n   112\t            message: \&quot;Missing required fields \&quot;,\n   113\t            status: false,\n   114\t        });\n   115\t    }\n   116\t    await Reading.findOne({\n   117\t        _id: req.body.reading_id,\n   118\t        returned: false,\n   119\t    }).then(async (resx) =&gt; {\n   120\t        if (!resx) {\n   121\t            return res.json({\n   122\t                code: 422,\n   123\t                message: \&quot;No data found\&quot;,\n   124\t                status: false,\n   125\t            });\n   126\t        } \n   127\t    });\n   128\t    await ReadingRecord.findOneAndUpdate({\n   129\t        reading_id: mongoose.Types.ObjectId(req.body.reading_id)\n   130\t    }, {\n   131\t        read_time: new Date()\n   132\t    }, {\n   133\t        upsert: true \n   134\t    });\n   135\t    res.json({\n   136\t        code: 200,\n   137\t        message: \&quot;Reading record created\&quot;,\n   138\t        status: true,\n   139\t    });\n   140\t\n   141\t    return res.end();\n   142\t});\n...\nPath: Routes/Admin.js\n...\n    85\t\n    86\tapp.post(\&quot;/settings\&quot;, AdminHelper, async (req, res) =&gt; {\n    87\t\tconst collection_type = req.header(\&quot;x-current-collection\&quot;) || \&quot;JYG\&quot;;\n    88\t\n    89\t\tconst { key, value } = req.body;\n    90\t\n    91\t\tawait Setting.findOne({ key, collection_type }).then(async (resx) =&gt; {\n    92\t\t\tif (resx) {\n    93\t\t\t\tawait Setting.updateOne(\n    94\t\t\t\t\t{ key, collection_type },\n    95\t\t\t\t\t{ $set: { value } },\n    96\t\t\t\t\t{ upsert: false }\n    97\t\t\t\t);\n    98\t\t\t} else {\n    99\t\t\t\tawait Setting.insertMany([{ key, value, collection_type }]);\n   100\t\t\t}\n   101\t\t\tres.json({\n   102\t\t\t\tcode: 200,\n   103\t\t\t\tdata: {\n   104\t\t\t\t\tkey,\n   105\t\t\t\t\tvalue,\n   106\t\t\t\t\tcollection_type,\n   107\t\t\t\t},\n   108\t\t\t\tmessage: \&quot;Operation Successful\&quot;,\n   109\t\t\t});\n   110\t\t\treturn res.end();\n   111\t\t});\n   112\t});\n...\n   125\t\tawait Admin.findOne({ email: req.body.email, collection_type }).then(async (resx) =&gt; {\n   126\t\t\tif (resx) {\n   127\t\t\t\tres.json({\n   128\t\t\t\t\tcode: 409,\n   129\t\t\t\t\tmessage: \&quot;Email already exists.\&quot;,\n   130\t\t\t\t\tsuccess: false,\n   131\t\t\t\t});\n   132\t\t\t\treturn res.end();\n   133\t\t\t} else {\n   134\t\t\t\tconst salt = await bcrypt.genSalt(10);\n   135\t\t\t\tconst hash = await bcrypt.hash(req.body.password, salt);\n   136\t\t\t\t// 2 : librarian, 3: Publisher\n   137\t\t\t\tvar super_admin = false; var role = false; var role = req.body.role;\n   138\t\t\t\tif (req.body.role == '1') { super_admin = true; }\n   139\t\t\t\tconst mAdmin = Admin({\n   140\t\t\t\t\tname: req.body.name,\n   141\t\t\t\t\temail: req.body.email,\n   142\t\t\t\t\tphone_number: req.body.phone,\n   143\t\t\t\t\tpassword: hash,\n   144\t\t\t\t\tis_active: true,\n   145\t\t\t\t\tsuper_admin: super_admin,\n   146\t\t\t\t\trole: role,\n   147\t\t\t\t\tcollection_type\n   148\t\t\t\t});\n...\n   499\t\n   500\tapp.post(\&quot;/deleteBanner\&quot;, AdminHelper, async function (req, res) {\n   501\t\tconsole.log(req.body);\n   502\t\tconst collection_type = req.header(\&quot;x-current-collection\&quot;) || \&quot;JYG\&quot;;\n   503\t\tif (req.body.delID) {\n   504\t\t\tawait Banner.findOneAndDelete({\n   505\t\t\t\t_id: req.body.delID,\n   506\t\t\t\tcollection_type\n   507\t\t\t}).then(async (resx) =&gt; {\n   508\t\t\t\tlet bannerimage = resx &amp;&amp; resx.bannerimage ? resx.bannerimage : '';\n   509\t\t\t\tif (fs.existsSync(bannerimage)) {\n   510\t\t\t\t\tfs.unlink(bannerimage, (err) =&gt; {\n   511\t\t\t\t\t\tif (err) {\n   512\t\t\t\t\t\t\treturn res.json({\n   513\t\t\t\t\t\t\t\tcode: 500,\n   514\t\t\t\t\t\t\t\tmessage: \&quot;Internal error at delete preview\&quot;,\n   515\t\t\t\t\t\t\t\tsuccess: false,\n   516\t\t\t\t\t\t\t});\n   517\t\t\t\t\t\t}\n   518\t\t\t\t\t});\n   519\t\t\t\t}\n   520\t\n   521\t\t\t\treturn res.json({ code: 200, message: \&quot;Banner deleted successfully\&quot; });\n   522\t\t\t});\n   523\t\t}\n   524\t\treturn res.json({ code: 422, message: \&quot;Banners Not Available.\&quot; });\n   525\t});\n...\nPath: Routes/Reserve.js\n...\n   172\tapp.post(\&quot;/allReservedeleteBook\&quot;, UserHelper, async (req, res) =&gt; {\n   173\t\tif (!req.body.delete_id) {\n   174\t\t\treturn res.json({\n   175\t\t\t\tcode: 422,\n   176\t\t\t\tmessage: \&quot;Missing required fields\&quot;,\n   177\t\t\t\tstatus: false,\n   178\t\t\t});\n   179\t\t}\n   180\t\tvar bookbulk = [];\n   181\t\n   182\t\n   183\t\tconst promises1 = req.body.delete_id.map(async (obj) =&gt; {\n   184\t\t\tbookbulk.push(obj);\n   185\t\t});\n   186\t\tconst results1 = await Promise.all(promises1);\n   187\t\tconst options = { ordered: true };\n   188\t\tvar query = { _id: bookbulk };\n   189\t\tvar data = { $set: { is_deleted: true } }\n   190\t\tconst result = await Reserve.updateMany(query, data);\n   191\t\treturn res.json({\n   192\t\t\tcode: 200,\n   193\t\t\tdata: req.body.delete_id,\n   194\t\t\tmessage: \&quot;Operation successful.\&quot;,\n   195\t\t});\n   196\t\n   197\t});\n   198\tmodule.exports = {\n   199\t    router: app\n   200\t};...\nPath: Routes/User.js\n...\n   115\t\n   116\tapp.post(\&quot;/register\&quot;, upload.none(), async (req, res) =&gt; {\n   117\t\tif (\n   118\t\t\t!req.body.email ||\n   119\t\t\t!req.body.password ||\n   120\t\t\t!req.body.name ||\n   121\t\t\t!req.body.phone_number\n   122\t\t) {\n   123\t\t\tres.setHeader(\&quot;Content-Type\&quot;, \&quot;application/json\&quot;);\n   124\t\t\tres.status(422).json({\n   125\t\t\t\tcode: 422,\n   126\t\t\t\tmessage: \&quot;Missing fields in request\&quot;,\n   127\t\t\t\tsuccess: false,\n   128\t\t\t});\n   129\t\t\treturn res.end();\n   130\t\t}\n   131\t\tawait User.findOne({ email: req.body.email }).then(async (resx) =&gt; {\n   132\t\t\tif (resx) {\n   133\t\t\t\tres.json({\n   134\t\t\t\t\tcode: 409,\n   135\t\t\t\t\tmessage: \&quot;Email already exists.\&quot;,\n   136\t\t\t\t\tsuccess: false,\n   137\t\t\t\t});\n   138\t\t\t\treturn res.end();\n   139\t\t\t} else {\n   140\t\t\t\tconst salt = await bcrypt.genSalt(10);\n   141\t\t\t\tconst hashPassword = await bcrypt.hash(req.body.password, salt);\n   142\t\n   143\t\t\t\tconst mUser = new User({\n   144\t\t\t\t\temail: req.body.email,\n   145\t\t\t\t\tpassword: hashPassword,\n   146\t\t\t\t\tname: req.body.name,\n   147\t\t\t\t\tphone_number: req.body.phone_number,\n   148\t\t\t\t\tis_active: true,\n   149\t\t\t\t});\n...\n   188\t\n   189\tapp.post(\&quot;/getUserDetail\&quot;, upload.none(), async (req, res) =&gt; {\n   190\t\tif (req.body.user_id) {\n   191\t\t\tawait User.findOne({ _id: req.body.user_id }).then((foundData) =&gt; {\n   192\t\t\t\tres.json({\n   193\t\t\t\t\tcode: 200,\n   194\t\t\t\t\tmessage: \&quot;Operation Successful\&quot;,\n   195\t\t\t\t\tdata: {\n   196\t\t\t\t\t\tuser_id: foundData._id,\n   197\t\t\t\t\t\temail: foundData.email,\n   198\t\t\t\t\t\tphone_number: foundData.phone_number,\n   199\t\t\t\t\t\tname: foundData.name,\n   200\t\t\t\t\t}\n   201\t\t\t\t});\n   202\t\t\t\treturn res.end();\n   203\t\t\t});\n   204\t\t} else {\n   205\t\t\tres.json({\n   206\t\t\t\tcode: 422,\n   207\t\t\t\tmessage: \&quot;User ID in request\&quot;,\n   208\t\t\t\tsuccess: false,\n   209\t\t\t});\n   210\t\t\treturn res.end();\n   211\t\t}\n   212\t});\n   213\tapp.post(\&quot;/setUserDetail\&quot;, upload.none(), UserHelper, async (req, res) =&gt; {\n   214\t\tif (\n   215\t\t\t!req.body.email\n   216\t\t) {\n   217\t\t\tres.setHeader(\&quot;Content-Type\&quot;, \&quot;application/json\&quot;);\n   218\t\t\tres.status(422).json({\n   219\t\t\t\tcode: 422,\n   220\t\t\t\tmessage: \&quot;Missing Email-id\&quot;,\n   221\t\t\t\tsuccess: false,\n   222\t\t\t});\n   223\t\t\treturn res.end();\n   224\t\t}\n...\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}}},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;status&quot;:&quot;draft&quot;},&quot;draftActiveContextIds&quot;:[&quot;E:/HXL_Book/jrc-service/Routes/Borrow.js&quot;,&quot;E:/HXL_Book/jrc-servicefalsefalse&quot;,&quot;agentMemories&quot;],&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:true,&quot;extraData&quot;:{&quot;isAgentConversation&quot;:true,&quot;hasDirtyEdits&quot;:true},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;e82a7d21-97c4-4726-8f6c-2cbd7c13f863&quot;},&quot;d168409d-6909-43f5-abed-1feff27313a9&quot;:{&quot;id&quot;:&quot;d168409d-6909-43f5-abed-1feff27313a9&quot;,&quot;createdAtIso&quot;:&quot;2025-07-21T10:20:00.229Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-07-21T10:20:23.084Z&quot;,&quot;chatHistory&quot;:[{&quot;status&quot;:&quot;sent&quot;,&quot;request_id&quot;:&quot;f7550260-ddfc-4ed1-a4e2-0503c08838ec&quot;,&quot;request_message&quot;:&quot;也可以读一下其他文件，再来看看这个 server.js 有什么设计不合理的地方么&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;也可以读一下其他文件，再来看看这个 server.js 有什么设计不合理的地方么&quot;}]}]},&quot;mentioned_items&quot;:[],&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_01XC5u7hG42fWQffCw9iqUuo&quot;,&quot;tool_name&quot;:&quot;view&quot;,&quot;input_json&quot;:&quot;{\&quot;path\&quot;: \&quot;server.js\&quot;, \&quot;type\&quot;: \&quot;file\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}}],&quot;seen_state&quot;:&quot;unseen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;text_node&quot;:{&quot;content&quot;:&quot;也可以读一下其他文件，再来看看这个 server.js 有什么设计不合理的地方么&quot;}},{&quot;id&quot;:1,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;E:/HXL_Book/jrc-service&quot;,&quot;folderRoot&quot;:&quot;E:/HXL_Book/jrc-service&quot;}],&quot;workspaceFoldersUnchanged&quot;:false,&quot;currentTerminal&quot;:{&quot;terminalId&quot;:2,&quot;currentWorkingDirectory&quot;:&quot;E:/HXL_Book/jrc-service&quot;}}}],&quot;timestamp&quot;:&quot;2025-07-21T10:20:23.094Z&quot;,&quot;response_text&quot;:&quot;我来帮你分析 server.js 文件以及相关的代码结构，看看有什么设计不合理的地方。首先让我读取一些关键文件来了解整体架构。&quot;,&quot;workspace_file_chunks&quot;:[]}],&quot;feedbackStates&quot;:{&quot;temp-fe-a776f80a-7717-4193-a052-f5df6006ed5f&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;}},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;status&quot;:&quot;draft&quot;},&quot;draftActiveContextIds&quot;:[&quot;E:/HXL_Book/jrc-service/server.js&quot;,&quot;E:/HXL_Book/jrc-servicefalsefalse&quot;,&quot;agentMemories&quot;],&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:false,&quot;extraData&quot;:{&quot;isAgentConversation&quot;:true,&quot;hasDirtyEdits&quot;:true},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;8398dbb1-ae42-423a-b566-ba379ea3e866&quot;},&quot;27c668fc-6160-4234-adfe-6fa9d79c1054&quot;:{&quot;id&quot;:&quot;27c668fc-6160-4234-adfe-6fa9d79c1054&quot;,&quot;createdAtIso&quot;:&quot;2025-07-23T03:18:20.333Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-07-23T03:19:58.485Z&quot;,&quot;chatHistory&quot;:[{&quot;request_id&quot;:&quot;9b20135a-65e4-46f1-9351-311b75742fc2&quot;,&quot;uuid&quot;:&quot;cc8f1a99-cc5f-4444-81ea-2becf667ecc4&quot;,&quot;chatItemType&quot;:&quot;agentic-checkpoint-delimiter&quot;,&quot;status&quot;:&quot;success&quot;,&quot;fromTimestamp&quot;:0,&quot;toTimestamp&quot;:1753240700336,&quot;seen_state&quot;:&quot;seen&quot;},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;f1017be6-b3d7-498b-a23f-e1f99e5f5bd9&quot;,&quot;request_message&quot;:&quot;已经修复过 updateAvailableStock  isBookInStock。所以其实我觉得是没问题的。但是反馈有问题，题一：预约用户在收到成功预约书籍电邮，及书籍自动借入帐户后，但是在后台没有扣减1个复本\r\n如图1：用户A用browser A 和browser B 按 「立即阅读」，用户B 用browser C 按「立即预约」，用户A  关闭browser A 的阅读窗口，用户B成功收到预约书籍及电邮，但是后台却显示为1个available books，预期应该为0，因为目前有1个借阅，一个browser阅读中\r\n问题二：2c我的账户「歸還日期」多了一天，应该和电邮日期一致为05-08-2025&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;已经修复过 updateAvailableStock  isBookInStock。所以其实我觉得是没问题的。但是反馈有问题，题一：预约用户在收到成功预约书籍电邮，及书籍自动借入帐户后，但是在后台没有扣减1个复本\r&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;如图1：用户A用browser A 和browser B 按 「立即阅读」，用户B 用browser C 按「立即预约」，用户A  关闭browser A 的阅读窗口，用户B成功收到预约书籍及电邮，但是后台却显示为1个available books，预期应该为0，因为目前有1个借阅，一个browser阅读中\r&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;问题二：2c我的账户「歸還日期」多了一天，应该和电邮日期一致为05-08-2025&quot;}]}]},&quot;mentioned_items&quot;:[],&quot;structured_output_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:1,&quot;type&quot;:2,&quot;content&quot;:&quot;\n\n---\n\n*<NAME_EMAIL> has been suspended. To continue, [purchase a subscription](https://app.augmentcode.com/account).*&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;text_node&quot;:{&quot;content&quot;:&quot;已经修复过 updateAvailableStock  isBookInStock。所以其实我觉得是没问题的。但是反馈有问题，题一：预约用户在收到成功预约书籍电邮，及书籍自动借入帐户后，但是在后台没有扣减1个复本\r\n如图1：用户A用browser A 和browser B 按 「立即阅读」，用户B 用browser C 按「立即预约」，用户A  关闭browser A 的阅读窗口，用户B成功收到预约书籍及电邮，但是后台却显示为1个available books，预期应该为0，因为目前有1个借阅，一个browser阅读中\r\n问题二：2c我的账户「歸還日期」多了一天，应该和电邮日期一致为05-08-2025&quot;}},{&quot;id&quot;:1,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;E:/HXL_Book/jrc-service&quot;,&quot;folderRoot&quot;:&quot;E:/HXL_Book/jrc-service&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-23T03:19:42.049Z&quot;,&quot;response_text&quot;:&quot;\n\n---\n\n*<NAME_EMAIL> has been suspended. To continue, [purchase a subscription](https://app.augmentcode.com/account).*&quot;,&quot;workspace_file_chunks&quot;:[]}],&quot;feedbackStates&quot;:{&quot;temp-fe-5bbee1f9-273d-4b2e-9fa7-91f9ace4a46e&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;}},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;draftActiveContextIds&quot;:[&quot;E:/HXL_Book/jrc-servicefalse&quot;,&quot;userGuidelines&quot;,&quot;agentMemories&quot;],&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:true,&quot;extraData&quot;:{&quot;isAgentConversation&quot;:true,&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;be9f4bed-4833-4f22-a559-bfc85d33fd11&quot;},&quot;f0e065bc-56b2-42a7-baf3-8d4e7c21ab5d&quot;:{&quot;id&quot;:&quot;f0e065bc-56b2-42a7-baf3-8d4e7c21ab5d&quot;,&quot;createdAtIso&quot;:&quot;2025-07-23T06:46:30.744Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-07-23T06:46:30.744Z&quot;,&quot;chatHistory&quot;:[],&quot;feedbackStates&quot;:{},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:false,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;0f37c3a0-fc6e-4627-97fe-0fbd18fd544f&quot;},&quot;a960c370-db46-4b64-9144-33fd3ce78cda&quot;:{&quot;id&quot;:&quot;a960c370-db46-4b64-9144-33fd3ce78cda&quot;,&quot;createdAtIso&quot;:&quot;2025-07-23T06:46:30.900Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-07-23T07:56:23.241Z&quot;,&quot;chatHistory&quot;:[{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;1aeeea32-b0b7-4d29-b16b-837302ee2599&quot;,&quot;request_message&quot;:&quot;需要你修复这个地方。user.offset 值一般为 -480。2c我的账户「歸還日期」多了一天06-08-2025，应该和电邮日期一致为05-08-2025。即 datetoshow 相对准确，比存储了的 return_date&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;需要你修复这个地方。user.offset 值一般为 -480。2c我的账户「歸還日期」多了一天06-08-2025，应该和电邮日期一致为05-08-2025。即 datetoshow 相对准确，比存储了的 return_date&quot;}]}]},&quot;mentioned_items&quot;:[],&quot;structured_output_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:1,&quot;type&quot;:2,&quot;content&quot;:&quot;\n\n---\n\n*<NAME_EMAIL> has been suspended. To continue, [purchase a subscription](https://app.augmentcode.com/account).*&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;text_node&quot;:{&quot;content&quot;:&quot;需要你修复这个地方。user.offset 值一般为 -480。2c我的账户「歸還日期」多了一天06-08-2025，应该和电邮日期一致为05-08-2025。即 datetoshow 相对准确，比存储了的 return_date&quot;}},{&quot;id&quot;:1,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;E:/HXL_Book/jrc-service&quot;,&quot;folderRoot&quot;:&quot;E:/HXL_Book/jrc-service&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-23T07:56:23.261Z&quot;,&quot;response_text&quot;:&quot;\n\n---\n\n*<NAME_EMAIL> has been suspended. To continue, [purchase a subscription](https://app.augmentcode.com/account).*&quot;,&quot;workspace_file_chunks&quot;:[]}],&quot;feedbackStates&quot;:{&quot;temp-fe-eecf7518-68be-4c78-a322-0334c4939837&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;}},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:true,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;66d72e75-2373-4d44-817a-9150b9aace50&quot;}},&quot;agentExecutionMode&quot;:&quot;manual&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[],&quot;sortConversationsBy&quot;:&quot;lastMessageTimestamp&quot;,&quot;sendMode&quot;:&quot;send&quot;}" />
      </map>
    </option>
  </component>
</project>