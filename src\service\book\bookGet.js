const Book = require("../../../Model/Book");
const Admin = require("../../../Model/Admin");
const mongoose = require('mongoose');
const cloneDeep = require('lodash/cloneDeep');

const IS_DELETED = [{ is_deleted: { $exists: false } }, { is_deleted: { $exists: true, $eq: false } }];
const getIsDeleted = () => cloneDeep(IS_DELETED);

function regExpReplaceSpecialCharacters(str) {
    if (!str) return '';
    return str.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, "\\$&");
}

async function filterRecords(res, match, sortbycol, sortByType, collection_type) {
    console.log(match, 'filterRecords match');

    if (match.$or) {
        let or_condition = match.$or;
        if (or_condition.length > 0) {
            or_condition.map((e, index) => {
                or_condition[index] = { ...e, $or: getIsDeleted() }
            });
        };
    } else {
        match = {
            ...match,
            $or: getIsDeleted()
        }
    }

    let _match = collection_type ? {$and: [{ collection_type }, match]} : match;

    try {
        let books = await Book.aggregate([
            { $match: _match },
            {
                $lookup: {
                    from: 'categories',
                    localField: 'category_id',
                    foreignField: '_id',
                    as: 'category'
                }
            },
            {
                $project: {
                    _id: 1,
                    title: 1,
                    isbn_no: 1,
                    author: 1,
                    stock_quantity: 1,
                    available_quantity: 1,
                    cover_photo: 1,
                    book_pdf: 1,
                    preview_book: 1,
                    added_by: 1,
                    added_at: 1,
                    publish_date: 1,
                    publishingGroup: 1,
                    imprints: 1,
                    category_id: "$category._id",
                    category_name: "$category.name"
                }
            },
            { $sort: { [sortbycol]: sortByType === 'asc' ? 1 : -1 } }
        ]);

        res.json({
            code: 200,
            data: books,
            message: "Operation successful."
        });
    } catch (error) {
        console.error('Error in filterRecords:', error);
        // NOTE status(500) 可能 可以不加
        res.status(500).json({
            code: 500,
            message: "Internal server error"
        });
    }

    /* Don't delete below commented code - 17-05-2022 */
	// var result = await Book.aggregate([
	// {
	// $match: match
	// },
	// {
	// "$sort":{"sortbycol": -1}
	// }, 
	// {
	// $project: {
	// "title": "$title",
	// "author": "$author",
	// "excerpt": "$excerpt",
	// "publish_date": "$publish_date",
	// "cover_photo": "$cover_photo"
	// }
	// }
	// ]).catch((err) => {
	// res.json({
	// code: 500,
	// message: "Internal error in query book",
	// success: false,
	// });
	// return res.end();
	// });
}

async function getBooks(req) {
    const collection_type = req.header("x-current-collection");
    const { searchText, email, sortBy: sortParam, category, is_available, query, queryType } = req.body;
    
    let sortBy = sortParam || 'added_at';
    let sortByType = 'desc';
    
    if (sortParam && sortParam.includes('-')) {
        const [field, type] = sortParam.split('-');
        sortBy = field;
        sortByType = type;
    }

    // Handle email-based search
    if (email) {
        let match = {
            $or: getIsDeleted()
        };

        if (searchText) {
            const regex = {$regex: regExpReplaceSpecialCharacters(searchText.trim()), $options: "i"};
            match = {
                "$and": [{
                    $or: [
                        { isbn_no: regex },
                        { title: regex },
                        { author: regex },
                        { excerpt: regex }
                    ]
                }, match]
            };
        }

        if(collection_type){
            match={$and: [{ collection_type },match]}
        }

        const result = await Book.aggregate([
            { $match: match },
            {
                $lookup: {
                    from: 'categories',
                    localField: 'category_id',
                    foreignField: '_id',
                    as: 'categories'
                }
            },
            {
                $addFields: {
                    category_ids: "$category_id",
                    categories: "$categories.name"
                }
            },
            { $sort: { added_at: -1 } },
        ]);

        let sortedBooks = result;

        let booksArr = [];
        if (sortedBooks.length > 0) {
            sortedBooks = sortedBooks.sort((a, b) => b.count - a.count);
            const resx = await Admin.findOne({ email });
            
            for (let sortedBook of sortedBooks) {
                let c = 0;
                let cat = [];
                for (let category_id of sortedBook.category_id) {
                    cat.push({ _id: category_id, name: sortedBook.categories[c] });
                    c++;
                }

                let book = {
                    _id: sortedBook._id,
                    category_ids: cat,
                    category_id: sortedBook.categories,
                    cover_photo: sortedBook.cover_photo,
                    book_pdf: sortedBook.book_pdf,
                    preview_book: sortedBook.preview_book,
                    title: sortedBook.title,
                    isbn_no: sortedBook.isbn_no,
                    author: sortedBook.author,
                    stock_quantity: sortedBook.stock_quantity,
                    available_quantity: sortedBook.available_quantity,
                    publishingGroup: sortedBook.publishingGroup,
                    imprints: sortedBook.imprints,
                    publish_date: sortedBook.publish_date,
                    added_by: sortedBook.added_by
                };

                if (resx.role === '3' && ((book.added_by).toString()) === ((resx._id).toString())) {
                    booksArr.push(book);
                } else if (resx && (resx.role === '1' || resx.role === '2')) {
                    booksArr.push(book);
                }
            }
            return {
                code: 200,
                data: booksArr,
                message: "Operation successful."
            };
        } else {
            return {
                code: 200,
                data: booksArr,
                message: "Operation successful."
            };
        }
    }

    // Handle other filter combinations
    if (!category && !is_available && !query) {
        return { useFilter: true, match: {}, sortBy, sortByType };
    }

    if (category && !is_available && !query) {
        return { 
            useFilter: true, 
            match: { category_id: mongoose.Types.ObjectId(category) },
            sortBy,
            sortByType
        };
    }

    if (!category && is_available && !query) {
        return { 
            useFilter: true, 
            match: { available_quantity: { $gt: 0 } },
            sortBy,
            sortByType
        };
    }

    if (!category && !is_available && query) {
        return handleQueryFilter(query, queryType, null, false, sortBy, sortByType);
    }

    if (category && !is_available && query) {
        return handleQueryFilter(query, queryType, category, false, sortBy, sortByType);
    }

    if (category && is_available && !query) {
        return {
            useFilter: true,
            match: {
                $and: [
                    { available_quantity: { $gt: 0 } },
                    { category_id: mongoose.Types.ObjectId(category) }
                ]
            },
            sortBy,
            sortByType
        };
    }

    if (category && is_available && query) {
        return handleQueryFilter(query, queryType, category, true, sortBy, sortByType);
    }

    if (!category && is_available && query) {
        return handleQueryFilter(query, queryType, null, true, sortBy, sortByType);
    }

    return { useFilter: true, match: {}, sortBy, sortByType };
}

function handleQueryFilter(query, queryType, category, isAvailable, sortBy = 'added_at', sortByType = 'desc') {
    const regex = { $regex: regExpReplaceSpecialCharacters(query.trim()), $options: "i" };

    let conditions = [];

    if (queryType === '1') {
        conditions.push({title: regex});
    } else if (queryType === '2') {
        conditions.push({author: regex});
    } else if (queryType === '3') {
        conditions.push({excerpt: regex});
    } else if (queryType == '4') {
        conditions.push({
            $or: [
                { title: regex },
                { author: regex },
                { excerpt: regex }
            ]
        });
    }

    // Handle category filter
    if (category) {
        conditions.push({ category_id: mongoose.Types.ObjectId(category) });
    }

    // Handle availability filter
    if (isAvailable) {
        conditions.push({ available_quantity: { $gt: 0 } });
    }

    conditions.push({
            $or: getIsDeleted()
        });


    const match = conditions.length > 1 ? { $and: conditions } : conditions[0];

    return { 
        useFilter: true, 
        match,
        sortBy,
        sortByType
    };
}

module.exports = {
    getBooks,
    filterRecords
}