// 数据模型导入
const Borrow = require('../../../Model/Borrow');
const Book = require('../../../Model/Book');
const User = require('../../../Model/User');
const Category = require('../../../Model/Category');
const Reading = require('../../../Model/Reading');

/**
 * 获取阅读统计数据
 * @param {Date} start - 开始日期
 * @param {Date} end - 结束日期
 * @returns {Map} 阅读统计映射
 */
async function getReadingStats(start, end) {
    const readingStats = await Reading.aggregate([
        {
            $match: {
                read_date: { $gte: start, $lte: end }
            }
        },
        {
            $group: {
                _id: "$book_id",
                readCopies: { $sum: 1 }
            }
        }
    ]);

    const readingMap = new Map();
    readingStats.forEach(stat => {
        readingMap.set(stat._id.toString(), stat.readCopies);
    });

    return readingMap;
}

/**
 * 构建仅阅读结果
 * @param {Array} readOnlyBooks - 仅阅读的书籍
 * @param {Array} categories - 分类数据
 * @param {Map} readingMap - 阅读统计映射
 * @returns {Array} 仅阅读结果
 */
function buildReadOnlyResults(readOnlyBooks, categories, readingMap) {
    return readOnlyBooks.map(book => {
        const matchedCategories = (book.category_id || []).map(y => {
            const category = categories.find(z => z._id.toString() === y.toString());
            return category ? category.name : '';
        });

        return {
            isbnNumber: book.isbn_no,
            bookName: book.title,
            publishGroup: book.publishingGroup,
            imprints: book.imprints,
            authorName: book.author,
            category: matchedCategories.join(','),
            borrowedCopiesGroupByUserWithRenewedCopies: 0,
            borrowedCopiesGroupByUserWithoutRenewedCopies: 0,
            borrowedCopiesGroupByBookWithRenewedCopies: 0,
            borrowedCopiesGroupByBookWithoutRenewedCopies: 0,
            readCopies: readingMap.get(book._id.toString()) || 0,
            patronId: '-'
        };
    });
}

/**
 * 生成借阅详情报告
 *
 * 该函数通过 MongoDB 聚合管道生成详细的借阅统计报告，包含：
 * 1. 按用户-书籍分组的借阅统计（每个用户对每本书的借阅情况）
 * 2. 按书籍分组的总体借阅统计（每本书的总借阅情况）
 * 3. 未被借阅的其他书籍信息
 *
 * @param {number} startDate - 开始日期时间戳（Unix timestamp）
 * @param {number} endDate - 结束日期时间戳（Unix timestamp）
 * @param {string} collection_type - 集合类型（如 'JYG', 'XSTD', 'LHZ'）
 * @returns {Array} 借阅详情报告数据数组，按借阅次数降序排列
 */
async function generateBorrowDetailsReport(startDate, endDate, collection_type) {
    // ========== 第一步：构建基础聚合条件 ==========

    // 初始化聚合管道条件数组，用于复用基础匹配和投影逻辑
    const condition = [];

    // 构建借阅日期匹配条件：匹配在指定时间范围内的任一借阅日期
    // 包括：初次借阅日期、第一次续借日期、第二次续借日期
    const borrowMatchCondition = {
        $or: [
            {
                // 初次借阅日期在范围内
                issue_date: {
                    $gte: new Date(Number(startDate) * 1000),
                    $lte: new Date(Number(endDate) * 1000),
                }
            },
            {
                // 第一次续借日期在范围内
                reborrow_one_date: {
                    $gte: new Date(Number(startDate) * 1000),
                    $lte: new Date(Number(endDate) * 1000),
                }
            },
            {
                // 第二次续借日期在范围内
                reborrow_two_date: {
                    $gte: new Date(Number(startDate) * 1000),
                    $lte: new Date(Number(endDate) * 1000),
                }
            }
        ]
    };

    // 将匹配条件和投影逻辑添加到基础条件数组中
    condition.push({
        // 第一阶段：匹配符合日期范围的借阅记录
        $match: borrowMatchCondition
    }, {
        // 第二阶段：投影和计算续借次数
        $project: {
            // 根据续借状态计算包含续借的借阅次数
            countWithRenewedCopies: {
                $switch: {
                    branches: [
                        {
                            // 情况1：未续借（reborrowed_once=false, reborrowed_twice=false）
                            case: {
                                $and: [
                                    { $eq: ["$reborrowed_once", false] },
                                    { $eq: ["$reborrowed_twice", false] }
                                ]
                            },
                            then: 1  // 计为1次借阅
                        }, {
                            // 情况2：续借1次（reborrowed_once=true, reborrowed_twice=false）
                            case: {
                                $and: [
                                    { $eq: ["$reborrowed_once", true] },
                                    { $eq: ["$reborrowed_twice", false] }
                                ]
                            },
                            then: 2  // 计为2次借阅（初借+续借1次）
                        }, {
                            // 情况3：续借2次（reborrowed_once=true, reborrowed_twice=true）
                            case: {
                                $and: [
                                    { $eq: ["$reborrowed_once", true] },
                                    { $eq: ["$reborrowed_twice", true] }
                                ]
                            },
                            then: 3  // 计为3次借阅（初借+续借2次）
                        }
                    ],
                    default: 1  // 默认情况计为1次
                }
            },
            // 保留用户ID和书籍ID用于后续分组
            user_id: "$user_id",
            book_id: "$book_id",
        }
    });

    // ========== 第二步：构建按用户-书籍分组的聚合管道 ==========

    // 复用基础条件，添加按用户ID和书籍ID分组的逻辑
    // 用于统计每个用户对每本书的借阅情况
    const conditionGroupByBookIdAndUserId = [].concat(condition, {
        // 第三阶段：按书籍ID和用户ID分组统计
        $group: {
            "_id": {
                "book_id": "$book_id",   // 书籍ID
                "user_id": "$user_id"    // 用户ID
            },
            // 累加包含续借的借阅次数
            countWithRenewedCopies: {
                $sum: "$countWithRenewedCopies"
            },
            // 累加不含续借的借阅次数（每条记录计为1）
            countWithoutRenewedCopies: {
                $sum: 1
            }
        }
    }, {
        // 第四阶段：重新投影，将嵌套的_id字段提取到顶层
        $project: {
            book_id: "$_id.book_id",
            user_id: "$_id.user_id",
            countWithRenewedCopies: "$countWithRenewedCopies",
            countWithoutRenewedCopies: "$countWithoutRenewedCopies"
        }
    });

    // ========== 第三步：构建按书籍分组的聚合管道 ==========

    // 复用基础条件，添加按书籍ID分组的逻辑
    // 用于统计每本书的总体借阅情况（所有用户的借阅汇总）
    const conditionGroupByBookId = [].concat(condition, {
        // 第三阶段：按书籍ID分组统计
        $group: {
            "_id": {
                "book_id": "$book_id"    // 只按书籍ID分组
            },
            // 累加该书的总借阅次数（含续借）
            countWithRenewedCopies: {
                $sum: "$countWithRenewedCopies"
            },
            // 累加该书的总借阅次数（不含续借）
            countWithoutRenewedCopies: {
                $sum: 1
            }
        }
    }, {
        // 第四阶段：重新投影，将嵌套的_id字段提取到顶层
        $project: {
            book_id: "$_id.book_id",
            countWithRenewedCopies: "$countWithRenewedCopies",
            countWithoutRenewedCopies: "$countWithoutRenewedCopies"
        }
    });

    // ========== 第四步：执行聚合查询 ==========

    // 并行执行两个聚合查询以提高性能
    const resultGroupByBookIdAndUserId = await Borrow.aggregate(conditionGroupByBookIdAndUserId);
    const resultGroupByBookId = await Borrow.aggregate(conditionGroupByBookId);

    // ========== 第五步：获取相关的书籍、用户和分类数据 ==========

    // 从聚合结果中提取所有涉及的书籍ID和用户ID（处理可能为空的情况）
    const bookIds = (resultGroupByBookId || []).map(x => x.book_id);
    const userIds = (resultGroupByBookIdAndUserId || []).map(x => x.user_id);

    // 并行查询书籍、用户和分类信息
    const books = bookIds.length > 0 ? await Book.find({
        _id: {
            $in: bookIds
        }
    }) : [];

    const users = userIds.length > 0 ? await User.find({
        _id: {
            $in: userIds
        }
    }) : [];

    // 获取所有分类信息用于后续映射
    const categories = await Category.find();

    // ========== 第六步：构建借阅统计结果 ==========

    // 将聚合结果映射为最终的业务对象（处理可能为空的情况）
    let items = (resultGroupByBookIdAndUserId || []).map(x => {
        // 根据book_id查找对应的书籍信息
        const book = books.find(y => y._id.toString() === x.book_id.toString());

        // 如果指定了collection_type，过滤不匹配的书籍
        if (collection_type && book.collection_type !== collection_type) return null;

        // NOTE 根据user_id查找对应的用户信息
        const user = users.find(y => y._id.toString() === x.user_id.toString());

        // 构建书籍分类字符串：将分类ID数组转换为分类名称字符串
        const matchedCategories = (book.category_id || []).map(y => {
            const category = categories.find(z => z._id.toString() === y.toString());
            return category ? category.name : '';
        });

        // 查找该书籍的总体借阅统计（按书籍分组的结果，处理可能为空的情况）
        const subResultGroupByBook = (resultGroupByBookId || []).find(y => y.book_id.toString() === x.book_id.toString());

        // 构建最终的数据对象
        return {
            isbnNumber: book.isbn_no,                                                    // ISBN号
            bookName: book.title,                                                       // 书名
            publishGroup: book.publishingGroup,                                         // 出版集团
            imprints: book.imprints,                                                   // 出版社
            authorName: book.author,                                                   // 作者
            category: matchedCategories.join(','),                                     // 分类（逗号分隔）
            readCopies: 0,                                                            // 阅读次数（借阅记录固定为0）
            borrowedCopiesGroupByUserWithRenewedCopies: x.countWithRenewedCopies,     // 该用户借阅次数（含续借）
            borrowedCopiesGroupByUserWithoutRenewedCopies: x.countWithoutRenewedCopies, // 该用户借阅次数（不含续借）
            borrowedCopiesGroupByBookWithRenewedCopies: subResultGroupByBook ? subResultGroupByBook.countWithRenewedCopies : 0,     // 该书总借阅次数（含续借）
            borrowedCopiesGroupByBookWithoutRenewedCopies: subResultGroupByBook ? subResultGroupByBook.countWithoutRenewedCopies : 0, // 该书总借阅次数（不含续借）
            patronId: user ? user.patronid : '-'                                      // 用户patron ID
        };
    }).filter(x => !!x);  // 过滤掉null值

    // ========== 第七步：获取阅读统计数据并更新借阅记录的readCopies ==========

    // 解析日期范围（使用moment保持与原逻辑一致）
    const moment = require('moment-timezone');
    const start = moment.unix(startDate).tz('Asia/Hong_Kong').startOf('day').toDate();
    const end = moment.unix(endDate).tz('Asia/Hong_Kong').endOf('day').toDate();

    // 获取阅读统计映射
    const readingMap = await getReadingStats(start, end);

    // 获取已借阅书籍的ISBN集合，用于排除
    const borrowedISBNs = new Set(items.map(x => x.isbnNumber));

    // 获取所有有阅读记录的书籍（与setdetail保持一致，不过滤已借阅的书籍）
    const readOnlyBookIds = Array.from(readingMap.keys());

    const readOnlyBooks = await Book.find({ _id: { $in: readOnlyBookIds } }).lean();
    const onlyReadResults = buildReadOnlyResults(readOnlyBooks, categories, readingMap);

    // ========== 第八步：获取未被借阅的其他书籍 ==========

    // 通过聚合管道查询未被借阅的书籍
    const otherBooks = await Book.aggregate([
        {
            // 第一阶段：匹配条件
            $match: {
                $and: [
                    {
                        // 书籍未被删除的条件
                        $or: [
                            { is_deleted: { $exists: false } },           // is_deleted字段不存在
                            { is_deleted: { $exists: true, $eq: false } }, // is_deleted字段存在且为false
                        ],
                    },
                    {
                        // 属于指定集合类型且ISBN不在已借阅书籍列表中
                        collection_type: collection_type,
                        isbn_no: {
                            // 排除已经在借阅统计和阅读统计中的书籍
                            $nin: (items || []).concat(onlyReadResults || []).map(x => x.isbnNumber).filter(x => !!x)
                        }
                    }
                ]
            }
        },
        {
            // 第二阶段：投影所需字段并设置默认值
            $project: {
                isbnNumber: "$isbn_no",                                    // ISBN号
                bookName: "$title",                                       // 书名
                publishGroup: "$publishingGroup",                         // 出版集团
                imprints: "$imprints",                                   // 出版社
                authorName: "$author",                                   // 作者
                category_id: "$category_id",                             // 分类ID（用于后续lookup）
                readCopies: { $literal: 0 },                           // 阅读次数（固定为0）
                borrowedCopiesGroupByUserWithRenewedCopies: { $literal: 0 },          // 用户借阅次数（含续借，固定为0）
                borrowedCopiesGroupByUserWithoutRenewedCopies: { $literal: 0 },       // 用户借阅次数（不含续借，固定为0）
                borrowedCopiesGroupByBookWithRenewedCopies: { $literal: 0 },          // 书籍总借阅次数（含续借，固定为0）
                borrowedCopiesGroupByBookWithoutRenewedCopies: { $literal: 0 },       // 书籍总借阅次数（不含续借，固定为0）
                patronId: { $literal: '-' }                            // patron ID（固定为'-'）
            }
        },
        {
            // 第三阶段：关联分类信息
            $lookup: {
                from: "categories",                                     // 关联categories集合
                localField: "category_id",                             // 本地字段
                foreignField: "_id",                                   // 外部字段
                as: "categories",                                      // 结果字段名
            },
        }
    ]);

    // ========== 第九步：合并结果并排序 ==========

    // 将其他书籍的分类ID数组转换为分类名称字符串
    const otherResults = otherBooks.map(x => ({
        ...x,
        category: x.categories.map(y => y.name).join(',')  // 将分类对象数组转换为逗号分隔的字符串
    }));

    // 合并三部分数据：借阅统计 + 仅阅读统计 + 其他书籍
    const finalResult = items.concat(onlyReadResults).concat(otherResults);

    // 过滤掉空值并按用户借阅次数（含续借）降序排列
    // 排序优先级：借阅次数多的记录 > 仅阅读的书籍 > 未使用的书籍
    return finalResult.filter(x => !!x).sort((a, b) =>
        b.borrowedCopiesGroupByUserWithRenewedCopies - a.borrowedCopiesGroupByUserWithRenewedCopies
    );
}

// ========== 模块导出 ==========

/**
 * 导出借阅详情报告生成函数
 *
 * 使用示例：
 * const { generateBorrowDetailsReport } = require('./borrowDetailsReport');
 * const report = await generateBorrowDetailsReport(1640995200, 1672531199, 'JYG');
 *
 * 返回数据结构：
 * [
 *   {
 *     isbnNumber: "978-7-111-12345-6",
 *     bookName: "JavaScript高级程序设计",
 *     publishGroup: "机械工业出版社",
 *     imprints: "机械工业出版社",
 *     authorName: "Nicholas C. Zakas",
 *     category: "计算机,编程语言",
 *     readCopies: 0,
 *     borrowedCopiesGroupByUserWithRenewedCopies: 3,     // 该用户借阅次数（含续借）
 *     borrowedCopiesGroupByUserWithoutRenewedCopies: 1,  // 该用户借阅次数（不含续借）
 *     borrowedCopiesGroupByBookWithRenewedCopies: 15,    // 该书总借阅次数（含续借）
 *     borrowedCopiesGroupByBookWithoutRenewedCopies: 8,  // 该书总借阅次数（不含续借）
 *     patronId: "12345678"
 *   },
 *   // ... 更多记录
 * ]
 */
module.exports = {
    generateBorrowDetailsReport,
    getReadingStats,
    buildReadOnlyResults
};
