const mongoose = require('mongoose');
const Reserve = require('../../models/Reserve');
const Book = require('../../models/Book');
const User = require('../../models/User');
const Borrow = require('../../models/Borrow');
const BookHelper = require('../../Helper/BookHelper');
const { sendBlockedMail, sendBorrowMail } = require('./emailAction');
const { MAX_BORROW_BOOK_NUM, MAX_BORROW_DAYS } = require('../../config/constants');

/**
 * 同步处理还书后的预约流程
 * @param {string} book_id - 书籍ID
 * @param {Object} [options] - 选项
 * @param {Object} [options.session] - Mongoose 会话（用于事务）
 */
async function syncReturnBooks(book_id, { session } = {}) {
    // 参数验证
    if (!book_id) {
        console.log('syncReturnBooks: book_id is required');
    }

    let today = new Date();
    let numberOfDaysToAddTo = 30;
    /* Find previous 30th day's date */
    today.setDate(today.getDate() - numberOfDaysToAddTo);

    const options = [
        {$match: { book_id: mongoose.Types.ObjectId(book_id), is_deleted: false }},
        {$lookup: {
            from: 'books',
            localField: 'book_id',
            foreignField: '_id',
            as: 'book'
        }},
        {$unwind: '$book'},
        {$lookup: {
            from: 'users',
            localField: 'user_id',
            foreignField: '_id',
            as: 'user'
        }},
        {$unwind: '$user'},
        {$sort: {created_at: 1}}
    ];
    
    // 获取预约信息
    let reservations = await Reserve.aggregate(options);

    console.log('reservations = ', reservations);

    if (reservations.length === 0) {
        return;
    } // 无预约数据，直接返回

    for (let result of reservations) {
        console.log('result = ', result);
        let book = await Book.findOne({ _id: mongoose.Types.ObjectId(result.book._id) });

        let sortedReserves = result.reserve.sort((a, b) => new Date(a.reserve_date) - new Date(b.reserve_date));

        for (let reserve of sortedReserves) {
            let reserveDate = new Date(reserve.reserve_date);

            // 过期预约直接删除
            if (reserveDate < today || (reserve.is_mailed && new Date(reserve.mail_date) < today)) {
                await Reserve.findByIdAndUpdate(
                    reserve._id,
                    { is_deleted: true },
                    { 
                        useFindAndModify: false,
                        ...(session && { session })
                    }
                );
                continue;
            }

            // 检查最新库存
            let books_avail = await BookHelper.isBookInStock(book_id);
            if (books_avail <= 0) {
                console.log('可用库存 books_avail = ', books_avail)
                console.log('没有库存，停止处理')
                break;
            } // 没有库存，停止处理

            if (!reserve.is_mailed && !reserve.is_blocked) {
                let user = await User.findById(reserve.user_id);
                if (!user) continue; // 用户不存在

                let countsBorrowed = await BookHelper.totalBookBorrowedInPresent(user._id, book.collection_type);
                if (countsBorrowed >= MAX_BORROW_BOOK_NUM) {
                    // 超过借阅上限，标记为 blocked
                    await Reserve.findByIdAndUpdate(reserve.id, { is_blocked: true });
                    await sendBlockedMail(user.email, book.title, user.name);
                    continue;
                }

                // 借阅逻辑
                let return_date = new Date();
                return_date.setDate(return_date.getDate() + MAX_BORROW_DAYS);
                return_date.setHours(23, 59, 0, 0);

                let issue_date = new Date();
                // 修复：使用归还日期而不是借阅日期来计算邮件中显示的日期
                let offsetDate = new Date(return_date.getTime() - user.offset * 60000);
                let weekDays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
                let datetoshow = `${offsetDate.getDate()}-${offsetDate.getMonth() + 1}-${offsetDate.getFullYear()} (${weekDays[offsetDate.getDay()]})`;

                // 标记预约已发送邮件，并添加借阅记录
                await Reserve.findByIdAndUpdate(reserve.id, { is_mailed: true, mail_date: new Date(), is_deleted: true });

                const mBorrow = new Borrow({
                    email: user.email,
                    user_id: user._id,
                    book_id: mongoose.Types.ObjectId(book_id),
                    issue_date: issue_date,
                    return_date: return_date,
                    returned: false,
                    is_deleted: false
                });
                await mBorrow.save({ ...(session && { session }) });
                // NOTE 库存变化，产生借
                await BookHelper.updateAvailableStock(book_id, 'borrow', { session });
                await sendBorrowMail(user.email, book.title, user.name, datetoshow);
            }
        }
    }
}

module.exports = {
    syncReturnBooks
};
