const app = require("express").Router();
const Category = require("../Model/Category");
const UserSession = require("../Model/UserSession");
const { AdminHelper } = require("../Helper/AdminHelper");
const Book = require("../Model/Book");
var multer = require("multer");
const AdminSession = require("../Model/AdminSession");
var upload = multer();

app.all("/*", (req, res, next) => next());

app.post("/getAll", upload.none(), async (req, res) => {
	
	Category.find({})
		.then((cats) => {
			res.json({ code: 200, data: cats, message: "Operation successful." });
		})
		.catch((err) => {
			res.json({
				message: "Internal error in creating category.",
				success: false,
				code: 500,
			});
		});
	
});
app.post("/getAllbookcategory", upload.none(), async (req, res) => {
	var catsdata = [];
  await Book.distinct("category_id", {
    $or: [
      { is_deleted: { $exists: false } },
      { is_deleted: { $exists: true, $eq: false } },
    ],
  }).then(async (resp) => {
    const promises = resp.map(async (resp_el) => {
      await Category.findOne({ _id: resp_el }).then((cats) => {
        if (cats) catsdata.push(cats);
      });
    });
    const results = await Promise.all(promises);
  });
	
	catsdata.sort(function(a, b){
		if(a && b){
		  let dateA = a._id;
		  let dateB = b._id;
		  if (dateA < dateB) 
		  {
			return -1;
		  }    
		  else if (dateA > dateB)
		  {
			return 1;
		  }   
		}
		  return 0;
	});

	res.json({ code: 200, data: catsdata, message: "Operation successful." });
	
});
app.post("/delete", AdminHelper, upload.none(), async (req, res) => {
  const ids = await Book.distinct("category_id", {
    $or: [
      { is_deleted: { $exists: false } },
      { is_deleted: { $exists: true, $eq: false } },
    ],
  });
  if (ids.map((x) => x.toString()).some((x) => x === req.body.category_id)) {
    return res.json({
      code: 422,
      message: "Exists related books",
      status: true,
    });
  }
  await Category.deleteOne({ _id: req.body.category_id }).then((deletedOne) => {
    return res.json({
      code: 200,
      message: "Deletion successful",
      status: true,
    });
  });
});

app.post("/edit", AdminHelper, upload.none(), async (req, res) => {
	if (!req.body.name || !req.body.category_id) {
		res.json({
			code: 422,
			message: "Missing fields in request",
			success: false,
		});
		return res.end();
	}
	await Category.findOneAndUpdate(
		{ _id: req.body.category_id },
		{ name: req.body.name }
	).then((deletedOne) => {
		return res.json({
			code: 200,
			message: "Modification successful",
			status: true,
		});
	});
});

app.post("/create", upload.none(), AdminHelper, async (req, res) => {
	if (!req.body.name) {
		res.json({
			code: 422,
			message: "Missing fields in request",
			success: false,
		});
		return res.end();
	}
	UserSession.findOne;
	const mCategory = new Category({
		name: req.body.name,
	});
	await Category.findOne({ name: {'$regex': req.body.name, '$options': 'i'}} ).then(async(ress) => {
		if(!ress)
		{
			await mCategory
			.save()
			.then((storedData) => {
				res.json({
					code: 200,
					message: "Create operation successful",
					data: {
						id: storedData._id,
						name: storedData.name,
					},
				});
				res.end();
			})
			.catch((err) => {
				res.json({
					message: "Internal error in creating category.",
					success: false,
					code: 500,
				});
				res.end();
			});
		}
		else{
			
				res.json({
					message: "Category Already Exist !",
					success: false,
					code: 500,
				});
				res.end();
			
		}
	});
	
});
module.exports = {
    router: app
};
