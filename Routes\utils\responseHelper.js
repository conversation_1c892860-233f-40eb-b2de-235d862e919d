/**
 * 统一响应格式工具类
 * 提供标准化的 API 响应格式
 */

class ResponseHelper {
    /**
     * 成功响应
     * @param {Object} res - Express response 对象
     * @param {*} data - 响应数据
     * @param {string} message - 响应消息
     * @param {number} code - 响应代码
     */
    static success(res, data = null, message = 'Operation successful', code = 200) {
        return res.json({
            code,
            message,
            status: true,
            data
        });
    }

    /**
     * 错误响应
     * @param {Object} res - Express response 对象
     * @param {number} code - 错误代码
     * @param {string} message - 错误消息
     */
    static error(res, code = 500, message = 'Internal Error') {
        return res.json({
            code,
            message,
            status: false
        });
    }

    /**
     * 验证失败响应
     * @param {Object} res - Express response 对象
     * @param {string} message - 验证失败消息
     */
    static validationError(res, message = 'Missing required fields') {
        return res.json({
            code: 422,
            message,
            status: false
        });
    }

    /**
     * 未授权响应
     * @param {Object} res - Express response 对象
     * @param {string} message - 未授权消息
     */
    static unauthorized(res, message = 'Unauthorized') {
        return res.json({
            code: 401,
            message,
            status: false
        });
    }

    /**
     * 资源不存在响应
     * @param {Object} res - Express response 对象
     * @param {string} message - 资源不存在消息
     */
    static notFound(res, message = 'Resource not found') {
        return res.json({
            code: 404,
            message,
            status: false
        });
    }
}

module.exports = ResponseHelper;
