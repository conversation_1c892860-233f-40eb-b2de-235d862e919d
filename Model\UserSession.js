const mongoose = require("mongoose");
const Schema = mongoose.Schema;

const UserSession = new Schema({
	user_id: {
		required: true,
		type: Schema.Types.ObjectId,
		ref: "User",
	},
	token: {
		required: true,
		type: String,
	},
	is_active: {
		required: true,
		type: Boolean,
	},
	ip: {
		required: false,
		type: String,
	},
	user_agent: {
		required: true,
		type: String,
	},
	device: {
		required: true,
		type: String,
	},
	date:{
		required : true,
		type: Date,
	},
	forceLogoutAt:{
		required : false,
		type: Date,
	},
	logoutAt:{
		required : false,
		type: Date,
	},
	timestamp: { type: Date, default: Date.now}
});

module.exports = mongoose.model("UserSession", UserSession);