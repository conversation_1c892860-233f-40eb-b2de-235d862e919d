const mongoose = require('mongoose');
const Reading = require('../../Model/Reading');
const ReadingRecord = require('../../Model/ReadingRecord');
const {updateAvailableStock} = require('../../Helper/BookHelper');
const { syncReturnBooks } = require('./bookActions');

/**
 * 批量处理过期的阅读记录，自动归还书籍
 * 该函数会查找所有未归还的阅读记录，检查是否已超过阅读时间
 * 如果超时未归还，则自动归还书籍并更新库存
 */
async function batchReturnReadingBooks() {
    const overdueReadings = await Reading.find({
        returned: false
    }).populate({
        path: 'book_id',
        select: '_id'
    });
    
    if (overdueReadings.length === 0) {
        return;
    }
    
    console.log("Processing batch return for", overdueReadings.length, "readings");
    
    for (const reading of overdueReadings) {
        try {
            const readingRecord = await ReadingRecord.findOne({
                reading_id: reading._id
            });
            
            if (!(readingRecord && readingRecord.read_time)) {
                console.log(`Skipping reading ${reading._id} - no valid reading record`);
                continue;
            }

            const now = new Date();
            const recordTime = new Date(readingRecord.read_time);
            const diffMinutes = (now - recordTime) / (1000 * 60);

            if (diffMinutes <= 2) {
                console.log(`Skipping reading ${reading._id} - not expired yet (${diffMinutes.toFixed(2)} minutes)`);
                continue;
            }

            // 使用事务确保数据一致性
            const session = await mongoose.startSession();
            session.startTransaction();
            
            try {
                // 更新库存（updateAvailableStock 内部已经包含库存检查）
                await updateAvailableStock(reading.book_id._id, 'return', { session });
                
                // 更新阅读记录为已归还
                await Reading.findByIdAndUpdate(
                    reading._id,
                    {
                        returned: true,
                        return_date: now
                    },
                    { session }
                );

                // 处理可能的预约
                await syncReturnBooks(reading.book_id._id, { session });
                
                // 提交事务
                await session.commitTransaction();
                console.log(`Successfully processed batch return - Client: ${reading.client_id}, Book: ${reading.book_id._id}, Reading ID: ${reading._id}`);
            } catch (error) {
                // 回滚事务
                await session.abortTransaction();
                console.error(`Error processing reading ${reading._id}:`, error);
                throw error; // 重新抛出错误，由上层处理
            } finally {
                session.endSession();
            }
            
        } catch (error) {
            console.error(`Error processing reading ${reading._id}:`, error);
            // 继续处理下一条记录，不中断整个批处理
        }
    }
}

module.exports = {
    batchReturnReadingBooks
};
