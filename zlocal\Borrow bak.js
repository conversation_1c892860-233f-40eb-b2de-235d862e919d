// app.get("/sync-return-books", async (req, res) => {
//     let message = "book return cron will run every day at 12:00 AM on " + new Date();
//     var output = '';
//     await syncReturnBooks();

    /*
    await Reserve.distinct("book_id").then(async (resp) => {
        resp.forEach(async (resp_el) => {
            var counts = await Reserve.find({
                book_id: resp_el,
                is_deleted: false
            }).countDocuments();

            if (counts > 0) {
                await Reserve.find({
                    book_id: resp_el,
                    is_deleted: false
                }).then(async (rest) => {
                    rest.forEach(async (rest_ell) => {
                        var datesthirty = new Date(rest_ell.reserve_date);
                        if (datesthirty < dateToDate) {
                            await Reserve.findOneAndUpdate({
                                    _id: rest_ell._id
                                }, {
                                    is_deleted: true
                                }, {
                                    useFindAndModify: false
                                },
                                (err, updated) => {});
                        }
                    });
                });

                var ismailedexist = 0;
                await Reserve.find({
                    book_id: resp_el,
                    is_deleted: false,
                    is_mailed: true
                }).then(async (ress) => {
                    ress.forEach(async (resp_ell) => {
                        var today = new Date();
                        var dates = new Date(resp_ell.mail_date);

                        if (resp_ell.is_mailed == 'true' && dates < today) {
                            await Reserve.findOneAndUpdate({
                                    _id: resp_ell._id
                                }, {
                                    is_deleted: true
                                }, {
                                    useFindAndModify: false
                                },
                                (err, updated) => {

                                });
                        } else {
                            ismailedexist++;
                        }

                    });

                });
                var books_avail = await BookHelper.isBookInStock(resp_el);
                var someDate = new Date();
                var numberOfDaysToAdd = 1;

                someDate.setDate(someDate.getDate() + numberOfDaysToAdd);
                if ((books_avail > 0) && (books_avail != ismailedexist)) {
                    var cc = 0;
                    for (var i = 0; i < books_avail; i++) {
                        await Reserve.findOne({
                            book_id: resp_el,
                            is_deleted: false,
                            is_mailed: false,
                            is_blocked: false
                        }).then(async (reserveuser) => {
                            if (reserveuser) {
                                var countsborrow = await Borrow.find({
                                    returned: false,
                                    user_id: reserveuser.user_id
                                }).countDocuments();
                                await User.findOne({
                                    _id: reserveuser.user_id
                                }).then(async (resx) => {
                                    if (!resx) {
                                        return res.json({
                                            code: 500,
                                            message: "Internal Error in getting users",
                                            status: false,
                                        });
                                    } else {
                                        var emailsenduser = resx.email;
                                        var patronid = resx.patronid;
                                        var patron_name = resx.name;
                                        var offset = resx.offset;
                                        if (countsborrow < 4) {
                                            await Reserve.updateOne({
                                                _id: reserveuser._id
                                            }, {
                                                $set: {
                                                    is_mailed: true,
                                                    mail_date: someDate,
                                                    is_deleted: true
                                                }
                                            },
                                            (err, updated) => {
                                                const date = new Date();

                                                    var temp = new Date(date);
                                                    var userTimezoneOffset = parseInt(offset) * 60000;
                                                    var newDate = new Date(temp.getTime() - userTimezoneOffset);
                                                    newDate.setDate(newDate.getDate() + 7);
                                                    date.setDate(date.getDate() + 7);
                                                    date.setHours(23, 59, 0, 0);
                                                    const return_date = date;
                                                    const dateissue = new Date();

                                                    var offsethour = parseInt(offset) / 60;
                                                    var hours = temp.getHours();

                                                    //newDate.setHours(hours + offset);
                                                    temp = newDate;
                                                    var datetoshow = '';
                                                    //if(resx.language == 'ch')
                                                    //{
                                                    var monthName = ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'][temp.getMonth()];
                                                    var weekday = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'][temp.getDay()];

                                                    datetoshow = temp.getDate() + '-' + (temp.getMonth() + 1) + '-' + temp.getFullYear() + ' (' + weekday + ')';
                                                    //}
                                                    //else{
                                                    // var monthName = ['Jan','Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'][temp.getMonth()];
                                                    // var weekday = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday','Saturday'][temp.getDay()];

                                                    // datetoshow = temp.getDate()+'-'+(temp.getMonth()+1)+'-'+temp.getFullYear()+' ('+weekday+')';
                                                    // }
                                                    if (updated != null) {


                                                        const mBorrow = Borrow({
                                                            email: resx.email,
                                                            user_id: reserveuser.user_id,
                                                            book_id: resp_el,
                                                            issue_date: dateissue,
                                                            return_date: return_date,
                                                            returned: false,
                                                            is_deleted: false
                                                        });

                                                        mBorrow
                                                            .save()
                                                            .then((stored) => {
                                                                (BookHelper.updateAvailableStock(resp_el, 'borrow'));
                                                                var reserveMail = emailsenduser;
                                                                Book.findOne({
                                                                    _id: resp_el
                                                                }).then((bookData) => {
                                                                    //if(resx.language == 'ch'){
                                                                    mailOptions = {
                                                                        to: reserveMail,
                                                                        from: '<EMAIL>',
                                                                        subject: '金閱閣電子書預約書籍通知',
                                                                        html: '親愛的 ' + patron_name + ' 讀者:<br/><br/>你在香港公共圖書館金閱閣電子書預約的《' + bookData.title + '》已借入你的帳戶內，期限至' + datetoshow + '。你可登入香港公共圖書館的<a href="https://joyread.club">金閱閣電子書</a>，透過瀏覽器線上閱讀。<br/><br/>多謝使用金閱閣電子書。'
                                                                    };
                                                                    mailer(mailOptions);
                                                                    //}
                                                                    // else{
                                                                    // mailOptions = {
                                                                    // to: reserveMail,
                                                                    // from: '<EMAIL>',
                                                                    // subject: 'Book reservation notice of e-book',
                                                                    // html: 'Dear reader '+ patron_name +':<br/><br/>The 《'+ bookData.title +'》 that you reserved in the Hong Kong Public Library e-book has been borrowed into your account, and the deadline is '+datetoshow+'. You can log in to the <a href="http://library-connect.com/store">e-books service</a> of the Hong Kong Public Library and read them online through your browser.<br/><br/>Thank you for using Ebook service.'
                                                                    // };
                                                                    // mailer(mailOptions);
                                                                    // }

                                                                });

                                                            });

                                                    }
                                                });
                                        } else {
                                            await Reserve.updateOne({
                                                    _id: reserveuser._id
                                                }, {
                                                    $set: {
                                                        is_blocked: true
                                                    }
                                                },

                                                (err, updated) => {
                                                    if (updated != null) {
                                                        var reserveMail = emailsenduser;
                                                        Book.findOne({
                                                            _id: resp_el
                                                        }).then((bookData) => {
                                                            //if(resx.language == 'ch'){
                                                            mailOptions = {
                                                                to: reserveMail,
                                                                from: '<EMAIL>',
                                                                subject: '金閱閣電子書預約書籍通知 (借閱冊數已滿)',
                                                                html: '親愛的 ' + patron_name + ' 讀者:<br/><br/> 由於你在香港公共圖書館金閱閣電子書的借閱額已滿，你預約的 《' + bookData.title + '》 將排到下一個順位。請登入香港公共圖書館<a href="https://joyread.club">金閱閣電子書</a>，歸還任何一本書籍。待讀者歸還該書後，便會自動借入你的帳戶。<br/><br/>多謝使用金閱閣電子書。'
                                                            };
                                                            mailer(mailOptions);
                                                            //}
                                                            // else {
                                                            // mailOptions = {
                                                            // to: reserveMail,
                                                            // from: '<EMAIL>',
                                                            // subject: 'e-book reservation notice (the number of borrowing books is full)',
                                                            // html: 'Dear reader '+ patron_name +':<br/><br/> Since your borrowing amount of the e-book at the Hong Kong Public Library Jinyuege is full, the 《'+ bookData.title +'》 you reserved will be ranked next. Please log in to the Hong Kong Public Library <a href="http://library-connect.com/store">e-books</a> account to return any book. After the reader returns the book, it will be automatically borrowed into your account.<br/><br/>Thank you for using Ebook service.'
                                                            // };
                                                            // mailer(mailOptions);
                                                            // }
                                                            //mailer(mailOptions);
                                                        });
                                                    }
                                                });
                                        }

                                    }
                                });

                            }
                        });

                    }

                }

            }
        });
    });
    */

//     console.log({ code: 200, data: output, message: message });
//     return res.send({
//         code: 200,
//         data: output,
//         message: message
//     });
// });
