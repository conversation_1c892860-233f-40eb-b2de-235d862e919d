const fs = require("fs");
const JSZip = require("jszip");

const addFilesFromDirectoryToZip = (directoryPath = "", zip) => {
  const directoryContents = fs.readdirSync(directoryPath, {
    withFileTypes: true,
  });
 
  directoryContents.forEach(({ name }) => {
    const path = `${directoryPath}/${name}`;

    if (fs.statSync(path).isFile()) {
      zip.file(path, fs.readFileSync(path, "utf-8"));
    }

    if (fs.statSync(path).isDirectory()) {
      addFilesFromDirectoryToZip(path, zip);
    }
  });
};

module.exports =  async (directoryPath = "") => {
  const zip = new JSZip();

  zip.file(
    "standalone.txt",
    "I will exist inside of the zip archive, but I'm not a real file here on the server."
  );

  addFilesFromDirectoryToZip(directoryPath, zip);

  const zipAsBase64 = await zip.generateAsync({ type: "base64" });

  return zipAsBase64;
};