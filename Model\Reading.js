const mongoose = require("mongoose");
const Schema = mongoose.Schema;
const Reading = new Schema({
    book_id: {
        type: Schema.Types.ObjectId,
        ref: "Book",
        required: true,
    },
	ip: {
		type: String,
		required: true,
	},
    client_id: {
        type: String,
		required: true,   	
    },
    read_date: {
        type: Date,
        required: true,
    },
    return_date: {
        type: Date,
        required: true,
    },
    returned: {
        type: Boolean,
        default: false,
    },
});
module.exports = mongoose.model("Reading", Reading);