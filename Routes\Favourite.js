const app = require("express").Router();
const { UserHelper } = require("../Helper/UserHelper");
const Book = require("../Model/Book");
const UserSession = require("../Model/UserSession");
var mailer = require('../Model/Mail');
const User = require("../Model/User");
const Favourite = require("../Model/Favourite");

app.use("/*", (req, res, next) => next());

app.post("/addtofav", UserHelper, async (req, res) => {
	
	if (!req.body[0].user_id || !req.body[0].book_id || !req.body[0].issue_date){
		return res.json({
			code: 422,
			message: "Missing required fields",
			status: false,
		});
	}
	if (await Favourite.find({ user_id: req.body[0].user_id, book_id: req.body[0].book_id }).countDocuments() > 0){
		return res.json({
			code: 422,
			message: "You've already add to favourite this!",
			status: false,
		});
	}
		
	var user_id = req.body.user_id;
	var book_id = req.body.book_id;
	 
	const favourite = Favourite({
		user_id: req.body[0].user_id,
		book_id: req.body[0].book_id,
		created_on: (req.body[0].issue_date).toString()
	});

	await favourite.save().then((stored) => {
		return res.json({
			code: 200,
			data: stored,
			message: "Operation successful.",
		});
	})
	.catch((err) => {
		return res.json({
			code: 500,
			message: "Internal Error in add to favourite.",
			status: false,
		});
	});
});

app.post("/detail", UserHelper, async (req, res) => {	
	if (!req.body.user_id) {
		return res.json({
			code: 422,
			message: "Missing required fields",
			status: false,
		});
	}

	let resp = await Favourite.find({ user_id: req.body.user_id }).sort({_id:-1}).populate("book_id");
	if (!resp) {
		return res.json({
			code: 422,
			message: "No data found",
			status: false,
		});
	}
	const response = [];
	for (let favData of resp) {
		if(favData.book_id){
			const data = { ...favData._doc };
			response.push(data);
		}
	}
	res.json({
		code: 200,
		message: "Operation successful.",
		data: response,
	});
	return res.end();
});

app.post("/deleteFavBook", UserHelper, async (req, res) => {
	if (!req.body.delete_id) {
		return res.json({
			code: 422,
			message: "Missing required fields",
			status: false,
		});
	}
	
	let doc = await Favourite.findOneAndDelete(
		{ _id: req.body.delete_id }
	);
	
	return res.json({
		code: 200,
		data: false,
		message: "Operation successful.",
	});

});

app.post("/allfavdeleteBook",UserHelper, async (req, res) => {
	if (!req.body.delete_id) {
		return res.json({
			code: 422,
			message: "Missing required fields",
			status: false,
		});
	}
	var bookbulk = [];
	
	const promises1 = req.body.delete_id.map(async(obj) => {
		bookbulk.push(obj);	
	});
	const results1 = await Promise.all(promises1);
	const options = { ordered: true };
	var query = { _id : bookbulk };
	var data = { $set : {is_deleted : true } }
	const result = await Favourite.deleteMany(query);
	return res.json({
		code: 200,
		data: req.body.delete_id,
		message: "Operation successful.",
	});
});

module.exports = {
    router: app
};