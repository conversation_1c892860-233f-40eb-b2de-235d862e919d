const express = require("express");
const cron = require('node-cron');
const app = express();
const http = require("http");
const https = require("https");
const fs = require("fs");
require("dotenv").config();
const mongoose = require("mongoose");
const bodyparser = require("body-parser");
const swaggerUi = require("swagger-ui-express");
const swaggerJsDoc = require("swagger-jsdoc");
const swaggerHelper = require("./Helper/SwaggerHelper");
const device = require("express-device");
const expressip = require("express-ip");

const path = require("path");
app.set('trust proxy', true);
const cors = require("cors"); 
app.use(cors({
	exposedHeaders: ['X-Country-Code']
}));
app.options('*', cors());
app.use(expressip().getIpInfoMiddleware);
app.use(device.capture());
app.use(bodyparser.json({limit: '50mb'}));
app.use(bodyparser.urlencoded({limit: '50mb', extended: true}));

mongoose.connect(process.env.DB_URL,  { useUnifiedTopology: true, useNewUrlParser : true, useCreateIndex: true, useFindAndModify: false } ).then((db) => {
	console.log("MongoDB connected");
})
.catch((error) => console.log("Lost Connection : " + error));

// 导入路由配置
const { setupRoutes } = require("./Routes");
const { batchReturnBorrowedBooks } = require("./src/action/borrowAction");
const { batchReturnReadingBooks } = require("./src/action/readAction");
const BorrowRouter = require("./Routes/Borrow");
const ReadingRouter = require("./Routes/Reading");

// 设置所有路由
setupRoutes(app);

const port = process.env.port;
// try {
	// const credentials = {
		// key: fs.readFileSync(process.env.SSL_PRIVATE_KEY, "utf8"),
		// cert: fs.readFileSync(process.env.SSL_CERTIFICATE, "utf8"),
		// ca: fs.readFileSync(process.env.SSL_CHAIN, "utf8"),
	// };

	// runSSLServer(credentials);
// } catch (e) {
	// console.log(e);
	// runUnsecureServer();
// }

runUnsecureServer();
function runUnsecureServer() {
	cron.schedule("1 0 0 * * *", async () => {
		const condition = {
			is_deleted: false,  
			returned: false,
			return_date: {
				$lt: new Date()
			}
    	}
		console.log("condition:", condition)
		await batchReturnBorrowedBooks(condition);
  	});

	cron.schedule("*/3 * * * *", async () => {
		await batchReturnReadingBooks();
    });
  
	const httpServer = http.createServer(app);
	httpServer.listen(port, () => {
		console.log("HTTP Server running on port " + port);
	}); 
	httpServer.timeout = 2800000;
}

function runSecureServer(credentials) {
	const httpsServer = https.createServer(app);
	httpsServer.listen(port, () => {
		console.log("HTTPS Server running on port " + port);
	});
	httpsServer.timeout = 2800000;
}

function runSSLServer(credentials) {
	if (process.env.DEBUG_ENVIRONMENT === "true") {
		runUnsecureServer();
	} else {
		runSecureServer(credentials);
	}
}

const swaggerConfig = {
	swaggerDefinition: swaggerHelper.definition,
	apis: ["server.js"],
};

const swaggerDoc = swaggerJsDoc(swaggerConfig);
app.use(
	"/ebook-api/documentation",
	swaggerUi.serve,
	swaggerUi.setup(swaggerDoc)
);


process.on("uncaughtException", function (err) {
  console.error("Error caught in uncaughtException event:", err);
});
