const AdminSession = require("../Model/AdminSession");

async function AdminHelper(req, res, next) {
	const SESSION_TOKEN = req.header("SESSION-TOKEN");
	if (!SESSION_TOKEN) {
		res.json({ code: 422, success: false, message: "Invalid request." });
		return res.end();
	}

	await AdminSession.findOne({ token: SESSION_TOKEN })
		.then((resx) => {
			if (!resx || !resx.is_active) {
				res.json({
					code: 422,
					success: false,
					message: "Invalid request.",
				});
				return res.end();
			}
			return next();
		})
		.catch((err) => {
			res.json({
				code: 500,
				message: "Internal error in header check",
				success: false,
			});
			return res.end();
		});
}

module.exports.AdminHelper = AdminHelper;