const mongoose = require("mongoose");
require("dotenv").config();

// 连接数据库
mongoose.connect(process.env.DB_URL, { 
    useUnifiedTopology: true, 
    useNewUrlParser: true, 
    useCreateIndex: true, 
    useFindAndModify: false 
}).then((db) => {
    console.log("MongoDB connected");
    testCopyManagement();
}).catch((error) => console.log("Lost Connection : " + error));

// 导入需要的模型和函数
const Reading = require('./Model/Reading');
const Book = require('./Model/Book');
const BookHelper = require('./Helper/BookHelper');

async function testCopyManagement() {
    try {
        console.log('\n=== 开始测试副本管理问题 ===\n');
        
        // 1. 查找一本有库存的书籍进行测试
        console.log('1. 查找测试书籍...');
        const book = await Book.findOne({ 
            is_deleted: { $ne: true },
            available_quantity: { $gt: 0 }
        });
        
        if (!book) {
            console.log('没有找到可用的测试书籍');
            return;
        }
        
        console.log(`测试书籍: ${book.title} (ID: ${book._id})`);
        console.log(`库存: ${book.available_quantity}/${book.stock_quantity}`);
        
        // 2. 模拟同一个 client_id 的多次阅读请求
        const testClientId = 'test_client_' + Date.now();
        const testIp = '*************';
        
        console.log(`\n2. 模拟 client_id: ${testClientId} 的阅读行为`);
        
        // 检查初始库存
        const initialStock = await BookHelper.isBookInStock(book._id);
        console.log(`初始库存: ${initialStock}`);
        
        // 第一次开始阅读
        console.log('\n--- 第一次开始阅读 ---');
        const reading1 = new Reading({
            book_id: book._id,
            client_id: testClientId,
            ip: testIp,
            read_date: new Date(),
            return_date: new Date(),
            returned: false
        });
        await reading1.save();
        await BookHelper.updateAvailableStock(book._id, 'reading');
        
        const stockAfterFirst = await BookHelper.isBookInStock(book._id);
        console.log(`第一次阅读后库存: ${stockAfterFirst}`);
        console.log(`Reading ID: ${reading1._id}`);
        
        // 第二次开始阅读（模拟同一个 client_id 打开新窗口）
        console.log('\n--- 第二次开始阅读（同一 client_id）---');
        
        // 检查是否已存在相同 client_id + book_id 的阅读记录
        const existingReading = await Reading.findOne({
            client_id: testClientId,
            book_id: book._id,
            returned: false
        });
        
        if (existingReading) {
            console.log(`发现已存在的阅读记录: ${existingReading._id}`);
            console.log('应该返回现有记录，不创建新记录，不减少库存');
        } else {
            console.log('没有找到现有记录，将创建新记录');
            const reading2 = new Reading({
                book_id: book._id,
                client_id: testClientId,
                ip: testIp,
                read_date: new Date(),
                return_date: new Date(),
                returned: false
            });
            await reading2.save();
            await BookHelper.updateAvailableStock(book._id, 'reading');
            console.log(`第二次 Reading ID: ${reading2._id}`);
        }
        
        const stockAfterSecond = await BookHelper.isBookInStock(book._id);
        console.log(`第二次阅读后库存: ${stockAfterSecond}`);
        
        // 3. 查看当前该 client_id 的所有阅读记录
        console.log('\n3. 查看当前阅读记录状态');
        const allReadings = await Reading.find({
            client_id: testClientId,
            book_id: book._id,
            returned: false
        });
        console.log(`client_id ${testClientId} 的未归还阅读记录数量: ${allReadings.length}`);
        allReadings.forEach((reading, index) => {
            console.log(`  ${index + 1}. Reading ID: ${reading._id}, IP: ${reading.ip}`);
        });
        
        // 4. 模拟归还第一个阅读记录
        if (allReadings.length > 0) {
            console.log('\n--- 归还第一个阅读记录 ---');
            const firstReading = allReadings[0];
            
            await Reading.updateOne(
                { _id: firstReading._id },
                { $set: { returned: true, return_date: new Date() } }
            );
            
            await BookHelper.updateAvailableStock(book._id, 'return');
            
            const stockAfterFirstReturn = await BookHelper.isBookInStock(book._id);
            console.log(`归还第一个记录后库存: ${stockAfterFirstReturn}`);
            
            // 检查是否还有其他未归还的记录
            const remainingReadings = await Reading.find({
                client_id: testClientId,
                book_id: book._id,
                returned: false
            });
            console.log(`剩余未归还记录数量: ${remainingReadings.length}`);
        }
        
        // 5. 如果还有记录，归还剩余的记录
        const finalReadings = await Reading.find({
            client_id: testClientId,
            book_id: book._id,
            returned: false
        });
        
        if (finalReadings.length > 0) {
            console.log('\n--- 归还剩余的阅读记录 ---');
            for (let reading of finalReadings) {
                await Reading.updateOne(
                    { _id: reading._id },
                    { $set: { returned: true, return_date: new Date() } }
                );
                
                await BookHelper.updateAvailableStock(book._id, 'return');
                
                const currentStock = await BookHelper.isBookInStock(book._id);
                console.log(`归还 ${reading._id} 后库存: ${currentStock}`);
            }
        }
        
        // 6. 最终检查库存是否恢复正常
        const finalStock = await BookHelper.isBookInStock(book._id);
        console.log(`\n最终库存: ${finalStock}`);
        console.log(`初始库存: ${initialStock}`);
        
        if (finalStock === initialStock) {
            console.log('✅ 库存恢复正常');
        } else {
            console.log('❌ 库存没有恢复到初始状态');
        }
        
        console.log('\n=== 测试完成 ===');
        
    } catch (error) {
        console.error('测试过程中出现错误:', error);
    } finally {
        // 关闭数据库连接
        setTimeout(() => {
            mongoose.connection.close();
            process.exit(0);
        }, 2000);
    }
}
