const mongoose = require("mongoose");
const Schema = mongoose.Schema;
const Admin = new Schema({
	name: {
		type: String,
		required: true,
	},
	email: {
		type: String,
		required: true,
	},
	phone_number: {
		type: String,
		required: true,
	},
	password: {
		type: String,
		required: true,
	},
	is_active: {
		type: Boolean,
		required: true,
	},
	super_admin: {
		type: Boolean,
		default: false,
	},
	reset_password_token: {
		type: String
	},
	reset_password_expires: {
		type: String
	},
	role: {
		type: String,
		required: true,
		default: "2",
	},
	collection_type: {
		type: String,
		required: false,
		default:'JYG'
	},
});
module.exports = mongoose.model("Admin", Admin);