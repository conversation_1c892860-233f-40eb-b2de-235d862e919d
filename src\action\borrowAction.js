const fs = require('fs');
const mongoose = require('mongoose');
const BookHelper = require('../../Helper/BookHelper');
const Borrow = require('../../Model/Borrow');
const Reserve = require('../../Model/Reserve');
const Book = require('../../Model/Book');
const { syncReturnBooks } = require('./bookActions');

/**
 * 批量归还借阅的书籍
 * @param {Object} condition - 查询条件
 * @param {Object} [options] - 选项
 * @param {Object} [options.session] - Mongoose 会话（用于事务）
 */
async function batchReturnBorrowedBooks(condition, { session } = {}) {
    const options = session ? { session } : {};
    
    // Find all unreturned borrow logs
    let borrows = await Borrow.find(condition).lean(options);

    console.log("Found", borrows.length, "books to return");

    if (borrows.length <= 0) {
        return;
    }

    // Return all books
    for (const borrow of borrows) {
        try {
            console.log("Processing return for borrow:", borrow._id);
            const { user_id, book_id, _id } = borrow;
            
            // Update stock
            await BookHelper.updateAvailableStock(book_id, 'return', { session });
            
            // Update reserve status
            await Reserve.updateOne(
                { user_id: user_id, is_deleted: false },
                { $set: { is_blocked: false } },
                { ...options }
            );

            // Get book data for file cleanup
            const bookData = await Book.findOne({ _id: book_id }).lean(options);
            
            if (bookData) {
                // Clean up files if this is the last copy and book is marked for deletion
                const countsborrow = await Borrow.countDocuments({
                    book_id: mongoose.Types.ObjectId(book_id),
                    is_deleted: false,
                    returned: false
                }, options);

                if (countsborrow === 0 && bookData.is_deleted) {
                    const filesToDelete = [
                        bookData.preview_book,
                        bookData.book_pdf,
                        bookData.cover_photo
                    ].filter(Boolean);

                    for (const file of filesToDelete) {
                        if (fs.existsSync(file)) {
                            fs.unlink(file, (err) => {
                                if (err) console.error("Error deleting file", file, ":", err);
                            });
                        }
                    }
                }
            }

            // Process reservations for this book
            await syncReturnBooks(book_id, { session });

            // Update borrow record
            await Borrow.findByIdAndUpdate(
                _id,
                {
                    returned: true,
                    return_date: new Date(),
                },
                {
                    ...options,
                    useFindAndModify: false,
                }
            );
            
        } catch (error) {
            console.error("Error processing return for borrow", borrow?._id, ":", error);
            // Continue with next book even if one fails
        }
    }
}

module.exports = {
    batchReturnBorrowedBooks
};
