const mongoose = require("mongoose");
const Schema = mongoose.Schema;

const Borrow = new Schema({
    user_id: {
        type: Schema.Types.ObjectId,
        ref: "User",
        required: true,
    },
    book_id: {
        type: Schema.Types.ObjectId,
        ref: "Book",
        required: true,
    },
    email: {
        type: String,
    },
    issue_date: {
        type: Date,
        required: true,
    },
    return_date: {
        type: Date,
        required: true,
    },
    returned: {
        type: Boolean,
        default: false,
    },
    reborrowed_once: {
        type: Boolean,
        default: false
    },
    reborrow_one_date: {
        type: Date
    },
    reborrowed_twice: {
        type: Boolean,
        default: false
    },
    is_deleted: {
        type: Boolean,
        default: false
    },
    reborrow_two_date: {
        type: Date
    }
});

module.exports = mongoose.model("<PERSON><PERSON>", <PERSON><PERSON>);
