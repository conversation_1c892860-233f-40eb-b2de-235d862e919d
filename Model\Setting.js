const mongoose = require("mongoose");
const Schema = mongoose.Schema;

const Setting = new Schema(
  {
    key: {
      required: true,
      type: String,
    },
    value: {
      required: true,
      type: String,
    },
    collection_type: {
      type: String,
      required: false,
      default: "JYG",
    },
  },
  { timestamps: true }
);

module.exports = mongoose.model("Setting", Setting);
