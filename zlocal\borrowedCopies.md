# 借阅副本数统计逻辑说明

## 概述

在 `borrowDetailsReport` 接口中，有两个关键的统计字段：
- `borrowedCopiesGroupByUserWithRenewedCopies` (包含续借的借阅副本数)
- `borrowedCopiesGroupByUserWithoutRenewedCopies` (不含续借的借阅副本数)

## 字段详解

### 1. borrowedCopiesGroupByUserWithRenewedCopies (G栏)

**定义**：考虑续借次数的总借阅量统计

**计算逻辑**：
```javascript
userBookStats[key].countWithRenewedCopies += count;
```

**count 值的计算规则**：

#### 场景1：没有续借 (`!reborrowed_once && !reborrowed_twice`)
```javascript
if (inRange(issue_date)) count = 1;
```
- 借阅日期在统计范围内：`count = 1`
- 借阅日期不在统计范围内：`count = 0`

#### 场景2：续借一次 (`reborrowed_once && !reborrowed_twice`)
```javascript
const inIssue = inRange(issue_date);
const inRe1 = inRange(reborrow_one_date);
count = inIssue && inRe1 ? 2 : (inIssue || inRe1 ? 1 : 0);
```
- 借阅日期和续借日期都在范围内：`count = 2`
- 只有借阅日期或续借日期在范围内：`count = 1`
- 都不在范围内：`count = 0`

#### 场景3：续借两次 (`reborrowed_once && reborrowed_twice`)
```javascript
const inIssue = inRange(issue_date);
const inRe1 = inRange(reborrow_one_date);
const inRe2 = inRange(reborrow_two_date);
if (inIssue && inRe1 && inRe2) count = 3;
else if ((inIssue && inRe2) || (inRe1 && inRe2)) count = 2;
else if (inIssue || inRe1 || inRe2) count = 1;
```
- 借阅、续借1、续借2都在范围内：`count = 3`
- 有两个日期在范围内：`count = 2`
- 只有一个日期在范围内：`count = 1`
- 都不在范围内：`count = 0`

### 2. borrowedCopiesGroupByUserWithoutRenewedCopies (H栏)

**定义**：借阅记录数统计（不考虑续借次数）

**计算逻辑**：
```javascript
userBookStats[key].countWithoutRenewedCopies += 1;
```

**重要理解**：
- 这个 `+= 1` 是在处理每个借阅记录时执行的
- **每个借阅记录**（不管有没有续借）都贡献 `1` 个计数
- 如果用户归还后重新借同一本书，会产生新的借阅记录，每个记录都单独计数

**规则**：
- 统计的是**借阅记录的数量**，不是借阅行为的数量
- 一个借阅记录无论续借多少次，都只计为 `1`
- 多个借阅记录（同一用户多次借同一本书）会累加计数

## 实际例子

### 例子1：用户A借阅《JavaScript权威指南》

**借阅记录**：
- 2024-01-01：初次借阅 (`issue_date`)
- 2024-01-15：第一次续借 (`reborrow_one_date`)
- 2024-01-30：第二次续借 (`reborrow_two_date`)

**统计范围**：2024-01-01 到 2024-01-31

**计算结果**：
- `borrowedCopiesGroupByUserWithRenewedCopies = 3` (初借 + 续借1 + 续借2)
- `borrowedCopiesGroupByUserWithoutRenewedCopies = 1` (只算一次借阅记录)

### 例子2：用户B借阅《Vue.js实战》

**借阅记录**：
- 2023-12-20：初次借阅 (`issue_date`)
- 2024-01-10：第一次续借 (`reborrow_one_date`)

**统计范围**：2024-01-01 到 2024-01-31

**计算结果**：
- `borrowedCopiesGroupByUserWithRenewedCopies = 1` (只有续借1在范围内)
- `borrowedCopiesGroupByUserWithoutRenewedCopies = 1` (这是1个借阅记录)

### 例子4：用户D多次借阅《React实战》

**借阅记录**：
```javascript
// 第一次借阅记录
{
  issue_date: "2024-01-05",
  reborrow_one_date: "2024-01-20",
  reborrowed_once: true
}

// 第二次借阅记录（归还后重新借）
{
  issue_date: "2024-01-25",
  reborrowed_once: false
}
```

**统计范围**：2024-01-01 到 2024-01-31

**计算结果**：
- `borrowedCopiesGroupByUserWithRenewedCopies = 3` (第一次记录贡献2，第二次记录贡献1)
- `borrowedCopiesGroupByUserWithoutRenewedCopies = 2` (两个借阅记录，每个贡献1)

### 例子3：用户C借阅《Python编程》

**借阅记录**：
- 2024-01-05：初次借阅 (`issue_date`)
- 没有续借

**统计范围**：2024-01-01 到 2024-01-31

**计算结果**：
- `borrowedCopiesGroupByUserWithRenewedCopies = 1` (只有初次借阅)
- `borrowedCopiesGroupByUserWithoutRenewedCopies = 1` (一次借阅记录)

## 业务含义

### borrowedCopiesGroupByUserWithRenewedCopies
- **用途**：反映用户的**实际使用量**
- **意义**：续借表示用户持续使用，应该计入总使用量
- **应用场景**：
  - 统计书籍的**总流通次数**
  - 评估书籍的**受欢迎程度**
  - 计算**图书馆资源利用率**

### borrowedCopiesGroupByUserWithoutRenewedCopies
- **用途**：反映**独立的借阅事件数量**
- **意义**：不管续借多少次，都只算一次借阅行为
- **应用场景**：
  - 统计**借阅人次**
  - 计算**借阅事件数**
  - 分析**用户借阅行为模式**

## Dashboard vs CSV 数据差异分析

### 问题现象
- **Dashboard显示**：Borrowed copies = 458
- **CSV G栏显示**：Borrowed Copies per Patron ID(with renewed copies) = 455

### 可能原因

1. **统计逻辑差异**：
   - Dashboard可能统计所有借阅记录数（类似 `countWithoutRenewedCopies` 的总和）
   - CSV G栏统计的是 `countWithRenewedCopies` 的总和（包含续借计数）

2. **日期范围筛选差异**：
   - Dashboard和CSV可能使用不同的日期范围
   - 时区处理可能不一致

3. **数据过滤条件差异**：
   - collection_type 过滤条件
   - 用户权限过滤
   - 删除状态过滤

4. **续借逻辑处理**：
   - 某些续借记录可能在日期范围边界处理上有差异
   - 续借次数统计方式不同

### 建议解决方案

1. **统一统计逻辑**：确保Dashboard和CSV使用相同的计算方法
2. **检查日期范围**：验证两个接口使用的时间范围是否一致
3. **对比查询条件**：检查过滤条件是否相同
4. **添加调试日志**：在关键计算点添加日志，便于排查差异

## 代码位置

- **Service文件**：`src/service/book/borrowDetailsReport.js`
- **Route文件**：`Routes/Book.js` - `/borrowDetailsReport` 接口
- **计算函数**：`calculateBorrowStats()` 函数（第15-52行）
