const definition = {
	swagger: "2.0",
	info: {
		description: "Ebook api documentation",
		version: "1.0.0",
		title: "Ebook",
	},
  host: "127.0.0.1:5000",
  basePath: "/",
	tags: [
		{
			name: "User",
			description: "Authentication ",
		},
		{
			name: "Category",
			description: "Access to get and modify Category",
		},
		{
			name: "Book",
			description: "Access to get and modify Books",
		},
		{
			name: "Admin",
			description: "Access",
		},
		{
			name: "Borrow",
			description: "Borrow",
		},
	],
  schemes: ["http", "https"],
	paths: {
		"/user/login": {
			post: {
				tags: ["User"],
				summary: "For Login",
				description: "",
        operationId: "User Login",
				consumes: ["application/x-www-form-urlencoded"],
				produces: ["application/xml", "application/json"],
				parameters: [
					{
						in: "formData",
						name: "email",
						type: "string",
						description: "User Email",
						required: true,
					},
					{
						in: "formData",
						type: "string",
						name: "Password",
						description: "User Password",
						required: true,
					},
				],
				responses: {
					"200": {
						description: "Successful Login",
					},
					"409": {
						description: "Invalid email or password",
					},
					"422": {
						description: "Email doesn't exist",
					},
					"500": {
						description: "Some internal error",
					},
				},
			},
		},
		"/user/logout": {
			post: {
				tags: ["User"],
				summary: "For logout",
				description: "",
				operationId: "userLogout",
				consumes: ["application/x-www-form-urlencoded"],
				produces: ["application/xml", "application/json"],
				parameters: [
					{
						in: "header",
						name: "SESSION-TOKEN",
						type: "string",
						description: "User session token",
						required: true,
					},
				],
				responses: {
					"200": {
						description: "Successful Login",
					},
					"409": {
						description: "Invalid email or password",
					},
					"422": {
						description: "Email doesn't exist",
					},
					"500": {
						description: "Some internal error",
					},
				},
			},
		},
		"/user/register": {
			post: {
				tags: ["User"],
				summary: "For registration",
				description: "",
				operationId: "userRegister",
				consumes: ["application/x-www-form-urlencoded"],
				produces: ["application/xml", "application/json"],
				parameters: [
					{
						in: "formData",
						name: "email",
						type: "string",
						description: "User Email",
						required: true,
					},
					{
						in: "formData",
						type: "string",
						name: "password",
						description: "User Password",
						required: true,
					},
					{
						in: "formData",
						type: "string",
						name: "name",
						description: "User fullname",
						required: true,
					},
					{
						in: "formData",
						type: "string",
						name: "phone_number",
						description: "User phone_number",
						required: true,
					},
				],
				responses: {
					"200": {
						description: "Successful Login",
					},
					"409": {
						description: "Invalid email or password",
					},
					"422": {
						description: "Email doesn't exist",
					},
					"500": {
						description: "Some internal error",
					},
				},
			},
		},
		"/user/editAccess": {
			post: {
				tags: ["User"],
				summary: "For Activate or Block User",
				description: "",
				operationId: "editAccess",
				consumes: ["application/x-www-form-urlencoded"],
				produces: ["application/xml", "application/json"],
				parameters: [
					{
						in: "formData",
						name: "user_id",
						type: "string",
						description: "User id",
						required: true,
					},
					{
						in: "formData",
						type: "string",
						name: "status",
						description: "status true or false",
						required: true,
					},
					{
						in: "header",
						name: "SESSION-TOKEN",
						type: "string",
						description: "ADMIN session token",
						required: true,
					},
				],
				responses: {
					"200": {
						description: "Successful Login",
					},
					"409": {
						description: "Invalid email or password",
					},
					"422": {
						description: "Email doesn't exist",
					},
					"500": {
						description: "Some internal error",
					},
				},
			},
		},
		"/user/getAllUsers": {
			post: {
				tags: ["User"],
				summary: "to get list of Users",
				description: "",
				operationId: "listOfUsers",
				consumes: ["application/x-www-form-urlencoded"],
				produces: ["application/xml", "application/json"],
				parameters: [
					{
						in: "header",
						name: "SESSION-TOKEN",
						type: "string",
						description: "ADMIN session token",
						required: true,
					},
				],
				responses: {
					"200": {
						description: "Successful Login",
					},
					"409": {
						description: "Invalid email or password",
					},
					"422": {
						description: "Email doesn't exist",
					},
					"500": {
						description: "Some internal error",
					},
				},
			},
		},
		"/category/create": {
			post: {
				tags: ["Category"],
				summary: "For creating category",
				description: "",
				operationId: "catCreate",
				consumes: ["application/x-www-form-urlencoded"],
				produces: ["application/xml", "application/json"],
				parameters: [
					{
						in: "formData",
						name: "name",
						type: "string",
						description: "Category name",
						required: true,
					},
					{
						in: "header",
						name: "SESSION-TOKEN",
						type: "string",
						description: "ADMIN session token",
						required: true,
					},
				],
				responses: {
					"200": {
						description: "Successful",
					},
					"422": {
						description: "Missing fields or Parameters",
					},
					"500": {
						description: "Some internal error",
					},
				},
			},
		},
		"/category/getAll": {
			post: {
				tags: ["Category"],
				summary: "For getting all category",
				description: "",
				operationId: "catGetAll",
				consumes: ["application/x-www-form-urlencoded"],
				produces: ["application/xml", "application/json"],
				responses: {
					"200": {
						description: "Successful",
					},
					"500": {
						description: "Some internal error",
					},
				},
			},
		},
		"/book/get": {
			post: {
				tags: ["Book"],
				summary: "For getting all book according to either search or query",
				description: "",
				operationId: "bookGet",
				consumes: ["application/x-www-form-urlencoded"],
				produces: ["application/xml", "application/json"],
				parameters: [
					{
						in: "formData",
						name: "category",
						type: "string",
						description: "Provide category name",
						required: true,
					},
					{
						in: "formData",
						name: "is_available",
						type: "boolean",
						description: "Provide true or false",
						required: true,
					},
					{
						in: "query",
						name: "query",
						type: "string",
						enum: [
							"title",
							"excerpt",
							"stock_quantity",
							"author",
							"total_pages",
							"cost",
							"cover_photo",
							"category_id",
							"sold",
						],
						description: "Provide ANYONE OF THEM to UPDATE",
						required: true,
					},
				],
				responses: {
					"200": {
						description: "Successful ",
					},
					"422": {
						description: "Invalid request",
					},
					"500": {
						description: "Some internal error",
					},
				},
			},
		},
		"/book/getRecommended": {
			post: {
				tags: ["Book"],
				summary: "For getting books that borrow often",
				description: "",
				operationId: "bookGet",
				consumes: ["application/x-www-form-urlencoded"],
				produces: ["application/xml", "application/json"],
				responses: {
					"200": {
						description: "Successful ",
					},
					"500": {
						description: "Some internal error",
					},
				},
			},
		},
		"/book/create": {
			post: {
				tags: ["Book"],
				summary: "For add book object",
				description: "",
				operationId: "userBookCreate",
				consumes: ["application/x-www-form-urlencoded", "multipart/form-data"],
				produces: ["application/xml", "application/json"],
				parameters: [
					{
						in: "formData",
						name: "category_id",
						type: "string",
						description: "Provice id from any one of /category/getAll",
						required: true,
					},
					{
						in: "formData",
						type: "string",
						name: "title",
						description: "Book title",
						required: true,
					},
					{
						in: "formData",
						type: "string",
						name: "excerpt",
						description: "Short abstract or info",
						required: true,
					},
					{
						in: "formData",
						type: "integer",
						name: "stock_quantity",
						description: "eg. 99 ( PROVIDE Integer )",
						required: true,
					},
					{
						in: "formData",
						type: "string",
						name: "author",
						description: "book author",
						required: true,
					},
					{
						in: "formData",
						type: "integer",
						name: "total_pages",
						description: "book pages eg. 99",
						required: true,
					},
					{
						in: "formData",
						type: "string",
						name: "cost",
						description: "book cost eg 200$",
						required: true,
					},
					{
						in: "formData",
						type: "file",
						name: "cover_photo",
						description: "book coverpage",
						required: true,
					},
					{
						in: "header",
						name: "SESSION-TOKEN",
						type: "string",
						description: "User or Admin session token",
						required: true,
					},
				],
				responses: {
					"200": {
						description: "Successful",
					},
					"422": {
						description: "Missing field",
					},
					"500": {
						description: "Some internal error",
					},
				},
			},
		},
		"/book/update": {
			post: {
				tags: ["Book"],
				summary: "For updating a detail of book object",
				description: "",
				operationId: "bookUpdate",
				consumes: ["application/x-www-form-urlencoded", "multipart/form-data"],
				produces: ["application/xml", "application/json"],
				parameters: [
					{
						in: "header",
						name: "SESSION-TOKEN",
						type: "string",
						description: "ADMIN session token",
						required: true,
					},
					{
						in: "formData",
						name: "id",
						type: "string",
						description: "Book id",
						required: true,
					},
					{
						in: "formData",
						name: "value",
						type: "string",
						description: "Provide Values according to mentioned velow",
						required: "true,",
						enum: [
							"title",
							"excerpt",
							"stock_quantity ( integer )",
							"author",
							"total_pages( integer )",
							"cost ( String )",
							"cover_photo ( value = true )",
							"category_id ( ID )",
						],
					},
					{
						in: "query",
						name: "field",
						type: "string",
						enum: [
							"title",
							"excerpt",
							"stock_quantity",
							"author",
							"total_pages",
							"cost",
							"cover_photo ( only FILE  )",
							"category_id",
						],
						description: "Provide ANYONE OF THEM to UPDATE",
						required: true,
					},
				],
				responses: {
					"200": {
						description: "Successful",
					},
					"422": {
						description: "Invalid request or Field missing OR No book found",
					},
					"500": {
						description: "Some internal error",
					},
				},
			},
		},
		"/book/delete": {
			post: {
				tags: ["Book"],
				summary: "For delete book object",
				description: "",
				operationId: "bookDelete",
				consumes: ["application/x-www-form-urlencoded", "multipart/form-data"],
				produces: ["application/xml", "application/json"],
				parameters: [
					{
						in: "header",
						name: "SESSION-TOKEN",
						type: "string",
						description: "ADMIN session token",
						required: true,
					},
					{
						in: "formData",
						name: "book_id",
						type: "string",
						description: "Book id",
						required: true,
					},
				],
				responses: {
					"200": {
						description: "Successful",
					},
					"422": {
						description: "Invalid request or Field missing",
					},
					"500": {
						description: "Some internal error",
					},
				},
			},
		},
		"/admin/login": {
			post: {
				tags: ["Admin"],
				summary: "For Login",
				description: "",
				operationId: "loginAdmin",
				consumes: ["application/x-www-form-urlencoded"],
				produces: ["application/xml", "application/json"],
				parameters: [
					{
						in: "formData",
						name: "email",
						type: "string",
						description: "User Email",
						required: true,
					},
					{
						in: "formData",
						type: "string",
						name: "Password",
						description: "User Password",
						required: true,
					},
				],
				responses: {
					"200": {
						description: "Successful Login",
					},
					"409": {
						description: "Invalid email or password",
					},
					"422": {
						description: "Email doesn't exist",
					},
					"500": {
						description: "Some internal error",
					},
				},
			},
		},
		"/admin/logout": {
			post: {
				tags: ["Admin"],
				summary: "For logout",
				description: "",
				operationId: "adminLogout",
				consumes: ["application/x-www-form-urlencoded"],
				produces: ["application/xml", "application/json"],
				parameters: [
					{
						in: "header",
						name: "SESSION-TOKEN",
						type: "string",
						description: "User session token",
						required: true,
					},
				],
				responses: {
					"200": {
						description: "Successful Login",
					},
					"409": {
						description: "Invalid email or password",
					},
					"422": {
						description: "Email doesn't exist",
					},
					"500": {
						description: "Some internal error",
					},
				},
			},
		},
		"/borrow/book": {
			post: {
				tags: ["Borrow"],
				summary: "For borrow book",
				description: "",
				operationId: "borrowABOOK",
				consumes: ["application/x-www-form-urlencoded"],
				produces: ["application/xml", "application/json"],
				parameters: [
					{
						in: "header",
						name: "SESSION-TOKEN",
						type: "string",
						description: "User session token",
						required: true,
					},
					{
						in: "formData",
						name: "user_id",
						type: "string",
						description: "user id",
						required: true,
					},
					{
						in: "formData",
						name: "book_id",
						type: "string",
						description: "specific book id",
						required: true,
					},
					{
						in: "formData",
						name: "issue_date",
						type: "string",
						description: "Issue date of book ",
						required: true,
					},
					{
						in: "formData",
						name: "return_date",
						type: "string",
						description: "Return date of book",
						required: true,
					},
					{
						in: "formData",
						name: "Status",
						type: "boolean",
						description: "True or false",
						required: true,
					},
				],
				responses: {
					"200": {
						description: "Successful",
					},

					"422": {
						description: "Missing fields/Book is already borrowed/Stock Out",
					},
					"500": {
						description: "Some internal error",
					},
				},
			},
		},
		"/borrow/return": {
			post: {
				tags: ["Borrow"],
				summary: "to return a book",
				description: "",
				operationId: "returnBook",
				consumes: ["application/x-www-form-urlencoded"],
				produces: ["application/xml", "application/json"],
				parameters: [
					{
						in: "header",
						name: "SESSION-TOKEN",
						type: "string",
						description: "User session token",
						required: true,
					},
					{
						in: "formData",
						name: "borrow_id",
						type: "string",
						description: "Borrow id",
						required: true,
					},
				],
				responses: {
					"200": {
						description: "Successful",
					},

					"422": {
						description: "Missing fields/Book is already borrowed/Stock Out",
					},
					"500": {
						description: "Some internal error",
					},
				},
			},
		},
		"/borrow/renew": {
			post: {
				tags: ["Borrow"],
				summary: "to renew book",
				description: "",
				operationId: "renewBook",
				consumes: ["application/x-www-form-urlencoded"],
				produces: ["application/xml", "application/json"],
				parameters: [
					{
						in: "header",
						name: "SESSION-TOKEN",
						type: "string",
						description: "User session token",
						required: true,
					},
					{
						in: "formData",
						name: "borrow_id",
						type: "string",
						description: "Borrow id",
						required: true,
					},
				],
				responses: {
					"200": {
						description: "Successful / Can only borrow 2 time",
					},

					"422": {
						description: "Missing fields/Book is already borrowed/Stock Out",
					},
					"500": {
						description: "Some internal error",
					},
				},
			},
		},
		"/borrow/detail": {
			post: {
				tags: ["Borrow"],
				summary: "for list out borrow",
				description: "",
				operationId: "detail book",
				consumes: ["application/x-www-form-urlencoded"],
				produces: ["application/xml", "application/json"],
				parameters: [
					{
						in: "header",
						name: "SESSION-TOKEN",
						type: "string",
						description: "User session token",
						required: true,
					},
					{
						in: "formData",
						name: "borrow_id",
						type: "string",
						description: "Borrow id",
						required: true,
					},
				],
				responses: {
					"200": {
						description: "Successful",
					},
					"422": {
						description: "Missing fields/Book is already borrowed/Stock Out",
					},
					"500": {
						description: "Some internal error",
					},
				},
			},
		},
	},
};

module.exports = {
	definition,
};
