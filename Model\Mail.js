var nodemailer = require("nodemailer");
const mongoose = require("mongoose");
const Schema = mongoose.Schema;

let transporter = nodemailer.createTransport({
  host: "smtp.sendgrid.net",
  port: 465,
  secure: true, // secure:true for port 465, secure:false for port 587
  auth: {
    user: "apikey",
    pass: "*********************************************************************",
  },
});
let sendMail = (mailOptions) => {
  transporter.sendMail(mailOptions, (error, info) => {
    if (error) {
      return console.log(error);
    } else {
      return console.log("An e-mail has been sent to " + mailOptions.to);
    }
  });
};

module.exports = sendMail;