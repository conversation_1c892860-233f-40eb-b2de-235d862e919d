const mongoose = require("mongoose");
const Schema = mongoose.Schema;

const Favourite = new Schema({
	user_id: {
        type: Schema.Types.ObjectId,
        ref: "User",
        required: true,
    },
    book_id: {
        type: Schema.Types.ObjectId,
        ref: "Book",
        required: true,
    },
	created_on: {
		type: String,
		required: true,
	}
});
module.exports = mongoose.model("Favourite", Favourite);