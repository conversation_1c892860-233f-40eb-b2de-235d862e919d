/**
 * 路由配置入口文件
 * 统一管理所有路由模块
 */

const express = require('express');
const path = require('path');

// 路由配置数组
const routeConfig = [
    { path: '/category', module: require('./Category'), description: '分类管理路由' },
    { path: '/user', module: require('./User'), description: '用户管理路由' },
    { path: '/book', module: require('./Book'), description: '图书管理路由' },
    { path: '/reading', module: require('./Reading'), description: '阅读记录路由' },
    { path: '/admin', module: require('./Admin'), description: '管理员路由' },
    { path: '/reserve', module: require('./Reserve'), description: '预约管理路由' },
    { path: '/favourite', module: require('./Favourite'), description: '收藏管理路由' },
    { path: '/borrow', module: require('./Borrow'), description: '借阅管理路由' },
];

/**
 * 设置所有路由到 Express 应用
 * @param {Express} app - Express 应用实例
 */
function setupRoutes(app) {
    console.log('Setting up routes...');

    routeConfig.forEach(({ path, module, description }) => {
        try {
            // 获取路由器实例，兼容新旧导出格式
            const router = module.router || module;
            app.use(path, router);
            console.log(`✓ Route ${path} loaded successfully - ${description}`);
        } catch (error) {
            console.error(`✗ Failed to load route ${path}:`, error.message);
        }
    });

    // 设置静态文件路由
    app.use("/uploads", express.static(path.join(__dirname, "../uploads")));
    console.log('✓ Static files route /uploads loaded successfully');

    console.log('All routes setup completed.');
}

/**
 * 获取所有路由信息
 * @returns {Array} 路由配置数组
 */
function getRouteInfo() {
    return routeConfig.map(({ path, description }) => ({ path, description }));
}

module.exports = {
    setupRoutes,
    routeConfig,
    getRouteInfo
};
