const app = require("express").Router();
const https = require("https");
const { v4: uuidv4 } = require("uuid");
const jwt = require("jsonwebtoken");
const User = require("../Model/User");
const UserSession = require("../Model/UserSession");
const bcrypt = require("bcryptjs");
var multer = require("multer");
const { update } = require("../Model/User");
const { AdminHelper } = require("../Helper/AdminHelper");
const Admin = require("../Model/Admin");
var upload = multer();
const request = require('request');
const axios = require("axios");
const { UserHelper } = require("../Helper/UserHelper");
app.all("/*", (req, res, next) => next());

/* Get all Admin Users */
app.post("/getAllUsers", upload.none(), AdminHelper, async (req, res) => {
	const collection_type = req.header("x-current-collection") || "JYG";

	await Admin.find({ collection_type }).then((resx) => {
		if (!resx) {
			return res.json({
				code: 500,
				message: "Internal Error in getting users",
				status: false,
			});
		} else {
			let temp = [];
			resx.forEach((user) => {
				var type = "";
				if (user.super_admin == true || user.role == 1) {
					type = "Super Admin";
				} else if (user.role == 2) {
					type = "Librarian";
				} else if (user.role == 3) {
					type = "Publisher";
				} else {
					type = "User";
				}
				temp.push({
					name: user.name,
					email: user.email,
					_id: user._id,
					role: type,
					is_active: user.is_active,
					phone_number: user.phone_number,
				});
			});
			return res.json({
				code: 200,
				message: "Operation Successful.",
				data: temp,
			});
		}
	});
});

app.post("/editAccess", upload.none(), AdminHelper, async (req, res) => {
	const collection_type = req.header("x-current-collection") || "JYG";

	if (
		!req.body.user_id ||
		req.body.status == undefined ||
		req.body.status == null
	) {
		return res.json({
			code: 422,
			message: "Missing fields.",
			status: false,
		});
	}
	if (req.body.status)
		await Admin.findOneAndUpdate(
			{ _id: req.body.user_id, collection_type },
			{ is_active: true },
			{ useFindAndModify: false }
		)
			.then((updated) => {
				return res.json({
					code: 200,
					data: updated,
					message: "Operation successful",
				});
			})
			.catch((err) => {
				return res.json({
					code: 500,
					message: "Internal Error while updating user.",
					status: false,
				});
			});
	else
		await Admin.findOneAndUpdate(
			{ _id: req.body.user_id, collection_type },
			{ is_active: false },
			{ useFindAndModify: false }
		)
			.then((updated) => {
				return res.json({
					code: 200,
					data: updated,
					message: "Operation successful",
				});
			})
			.catch((err) => {
				return res.json({
					code: 500,
					message: "Internal Error while updating user.",
					status: false,
				});
			});
});

app.post("/register", upload.none(), async (req, res) => {
	if (
		!req.body.email ||
		!req.body.password ||
		!req.body.name ||
		!req.body.phone_number
	) {
		res.setHeader("Content-Type", "application/json");
		res.status(422).json({
			code: 422,
			message: "Missing fields in request",
			success: false,
		});
		return res.end();
	}
	await User.findOne({ email: req.body.email }).then(async (resx) => {
		if (resx) {
			res.json({
				code: 409,
				message: "Email already exists.",
				success: false,
			});
			return res.end();
		} else {
			const salt = await bcrypt.genSalt(10);
			const hashPassword = await bcrypt.hash(req.body.password, salt);

			const mUser = new User({
				email: req.body.email,
				password: hashPassword,
				name: req.body.name,
				phone_number: req.body.phone_number,
				is_active: true,
			});

			await mUser
				.save()
				.then((storedData) => {
					jwt.sign(
						{
							id: storedData._id,
						},
						process.env.JWT_SECRET,
						(err, token) => {
							if (err) console.log(err);
							const mUserSession = new UserSession({
								user_id: storedData._id,
								token: token,
								is_active: true,
								user_agent: req.get("User-Agent"),
								ip: req.connection.remoteAddress,
								device: req.device.type,
							});
							mUserSession.save();
							res.json({
								code: 200,
								message: "Registration Successful.",
								data: { token: `${token}` },
							});
						}
					);
				})
				.catch((err) => {
					res.json({
						code: 500,
						success: false,
						message: "Internal error in registering user.",
					});
				});
		}
	});
});

app.post("/getUserDetail", upload.none(), async (req, res) => {
	if (req.body.user_id) {
		await User.findOne({ _id: req.body.user_id }).then((foundData) => {
			res.json({
				code: 200,
				message: "Operation Successful",
				data: {
					user_id: foundData._id,
					email: foundData.email,
					phone_number: foundData.phone_number,
					name: foundData.name,
				}
			});
			return res.end();
		});
	} else {
		res.json({
			code: 422,
			message: "User ID in request",
			success: false,
		});
		return res.end();
	}
});
app.post("/setUserDetail", upload.none(), UserHelper, async (req, res) => {
	if (
		!req.body.email
	) {
		res.setHeader("Content-Type", "application/json");
		res.status(422).json({
			code: 422,
			message: "Missing Email-id",
			success: false,
		});
		return res.end();
	}
	await User.find({ email: req.body.email, _id: { $ne: req.body.user_id } }).then(async (resx) => {
		if (req.body.user_id) {
			await User.findByIdAndUpdate(
				req.body.user_id,
				{ email: req.body.email },

				(err, updated) => {
					if (err) {
						console.log("Internal error in update Email");
						res.json({
							code: 424,
							message: "Internal error in update Email",
							success: false,
						});
						return res.end();
					}
					else {
						res.json({
							code: 200,
							message: "Email-id updated Successfully.",
							success: false,
						});
						return res.end();
					}
				}
			);
		} else {
			res.json({
				code: 423,
				message: "User ID in request",
				success: false,
			});
			return res.end();
		}
	});

});

app.post("/login", upload.none(), async (req, res) => {
	if (!req.body.email) {
		res.json({
			code: 422,
			message: "Missing fields in request",
			success: false,
		});
		return res.end();
	}
	var today = new Date();
	await User.findOne({ patronid: req.body.email }).then(async (foundData) => {
		if (foundData) {
			var now = new Date(Date.now() + (60 * 60 * 1000));
			exptime = now.getTime() / 1000;
			jwt.sign(
				{
					id: uuidv4(),
				},
				process.env.JWT_SECRET,
				async (err, token) => {
					if (err) console.log(err);
					await UserSession.updateMany({ user_id: foundData._id }, { $set: { is_active: false } }, function (err, res) {
						if (err) throw err;

						User.updateOne({ patronid: req.body.email },
							{ $set: { offset: req.body.offset } },
							(err, updated) => {
							});
						const mUserSession = new UserSession({
							user_id: foundData._id,
							token: token,
							is_active: true,
							user_agent: req.get("User-Agent"),
							ip: req.connection.remoteAddress,
							device: req.device.type,
							date: today
						});
						mUserSession.save();
					});
					res.json({
						code: 200,
						message: "Login Successful.",
						data: {
							user_id: foundData._id,
							token: `${token}`,
							email: foundData.email,
							exp: exptime,
							phone_number: foundData.phone_number,
							name: foundData.name,
						},
					});
					res.end();
				}
			);
		}
		else {
			res.json({
				code: 422,
				message: "patron doesn't exist.",
				success: false,
			});
			return res.end();
		}
	});
});

app.post("/logout", upload.none(), async (req, res) => {
	if (!req.header("SESSION-TOKEN")) {
		res.json({
			code: 422,
			message: "Missing fields in request",
			success: false,
		});
		return res.end();
	}
	const updatedData = await UserSession.findOneAndUpdate(
		{ token: req.header("SESSION-TOKEN") },
		{ $set: { is_active: false, logoutAt: new Date() } },
		(err, updated) => {
			if (err) {
				console.log(err);
				return res.json({
					code: 500,
					message: "Internal error in Logout",
					success: false,
				});
			}
			res.json({
				code: 200,
				message: "Operation Successful",
				data: "",
			});
			return res.end();
		}
	);
});


app.post("/authorizedv2", async (req, res) => {

	const collection_type = req.header("x-current-collection") || "JYG";


	var today = new Date();

	const getAccessTokenApi = process.env.GET_ACCESS_TOKEN_API || "https://dev.slsnp.hkpl.gov.hk/porsso-a2/realms/hkpl/auth/authorization_code";
	const getUserInfoApi = process.env.GET_USER_INFO_API || "https://dev.slsnp.hkpl.gov.hk/porsso-a2/realms/hkpl/userinfo";

	let clientID = process.env.HKPL_CLIENT_ID
	let clientSecret = process.env.HKPL_CLIENT_SECRET
	let redirectUri = process.env.HKPL_REDIRECT_URI

	if (collection_type === 'XSTD') {
		clientID = process.env.HKPL_SW_CLIENT_ID
		clientSecret = process.env.HKPL_SW_CLIENT_SECRET
		redirectUri = process.env.HKPL_SW_REDIRECT_URI
	}

	if (collection_type === 'LHZ') {
		clientID = process.env.HKPL_TW_CLIENT_ID
		clientSecret = process.env.HKPL_TW_CLIENT_SECRET
		redirectUri = process.env.HKPL_TW_REDIRECT_URI
	}


	// Create access token
	const result = await axios.post(
		getAccessTokenApi,
		{
			clientID,
			clientSecret,
			redirectUri,
			code: req.body.code
		},
		{
			httpsAgent: new https.Agent({
				rejectUnauthorized: false,
			}),
		}
	);

	if (!result || !result.data) return res.status(422).send({ message: "failed", });

	if (result.data.code !== "I00101")
		return res.status(422).send({ data: result.data });

	const {
		data: {
			content: { accessToken },
		},
	} = result;

	// Fetch user profile
	const result2 = await axios.post(
		getUserInfoApi,
		{
			clientID,
			clientSecret,
			accessToken,
			userInfo: ["id", "sex", "phoneNo"],
		},
		{
			httpsAgent: new https.Agent({
				rejectUnauthorized: false,
			}),
		}
	);

	console.log('v2', result2)
	console.log('v2', result2.data)

	if (!result2 || !result2.data) {
		return res.status(422).send({ message: "failed", });
	}

	if (result2.data.code !== "I00101")
		return res.status(422).send({ data: result2.data });

	const {
		data: { content: userProfile },
	} = result2;

	// {
	//   "sex": "1",
	//   "id": "100000089",
	//   "phoneNo": ""
	// }
	const { id } = userProfile

	const decoded = {
		patronid: id,
		name: "",
		exp: Math.floor(new Date().getTime() / 1000) + 86400,
	}

	const token = accessToken

	User.findOne({ patronid: decoded.patronid }).then(async (foundData) => {
		if (foundData) {
			UserSession.updateMany({ user_id: foundData._id }, { $set: { is_active: false, forceLogoutAt: new Date() } }, function (err, data) {
				if (err) throw err;
				User.updateOne({ patronid: decoded.patronid }, { $set: { offset: 0, name: decoded.name } }, (err, updated) => {
					if (err) throw err;
					const mUserSession = new UserSession({
						user_id: foundData._id,
						token: token,
						is_active: true,
						user_agent: req.get("User-Agent"),
						ip: req.connection.remoteAddress,
						device: req.device.type,
						date: today,
					});
					mUserSession.save(async function (err, storedData) {
						if (err) throw err;
						res.json({
							code: 200,
							message: "Login Successful.",
							data: {
								user_id: foundData._id,
								token: `${token}`,
								email: foundData.email,
								exp: decoded.exp,
								phone_number: foundData.phone_number,
								name: foundData.name,
							},
						});
						res.end();
					});
				});
			});

		}
		else {
			const mUser = new User({
				patronid: decoded.patronid,
				name: decoded.name,
				is_active: true,
				login_date: today,
				offset: 0,
				email: ''
			});

			mUser.save(async function (err, storedData) {
				if (err) { throw err; }
				const mUserSession = new UserSession({
					user_id: storedData._id,
					token: token,
					is_active: true,
					user_agent: req.get("User-Agent"),
					ip: req.connection.remoteAddress,
					device: req.device.type,
					date: today
				});
				mUserSession.save(async function (err, storedData2) {
					if (err) { throw err; }
					res.json({
						code: 200,
						message: "Registration Successful.",
						data: {
							token: `${token}`,
							exp: decoded.exp,
							user_id: storedData2.user_id,
							email: storedData2.email,
							phone_number: storedData.phone_number,
							name: storedData2.name,
							is_active: storedData2.is_active
						}
					});
				});
			});
		}
	});
});

app.post("/authorized", async (req, res) => {
	console.log('authorized');
	var today = new Date();
	if (!req.body[0].id_token) {
		res.json({
			code: 422,
			message: "Missing fields in request",
			success: false,
		});
		return res.end();
	}
	var token = req.body[0].id_token;
	var decoded = jwt.decode(token);
	User.findOne({ patronid: decoded.patronid }).then(async (foundData) => {
		if (foundData) {
			UserSession.updateMany({ user_id: foundData._id }, { $set: { is_active: false, forceLogoutAt: new Date() } }, function (err, data) {
				if (err) throw err;
				User.updateOne({ patronid: decoded.patronid }, { $set: { offset: req.body[0].offset, name: decoded.name } }, (err, updated) => {
					if (err) throw err;
					const mUserSession = new UserSession({
						user_id: foundData._id,
						token: token,
						is_active: true,
						user_agent: req.get("User-Agent"),
						ip: req.connection.remoteAddress,
						device: req.device.type,
						date: today,
					});
					mUserSession.save(async function (err, storedData) {
						if (err) throw err;
						res.json({
							code: 200,
							message: "Login Successful.",
							data: {
								user_id: foundData._id,
								token: `${token}`,
								email: foundData.email,
								exp: decoded.exp,
								phone_number: foundData.phone_number,
								name: foundData.name,
							},
						});
						res.end();
					});
				});
			});

		}
		else {
			const mUser = new User({
				patronid: decoded.patronid,
				name: decoded.name,
				is_active: true,
				login_date: today,
				offset: req.body[0].offset,
				email: ''
			});

			mUser.save(async function (err, storedData) {
				if (err) { throw err; }
				const mUserSession = new UserSession({
					user_id: storedData._id,
					token: token,
					is_active: true,
					user_agent: req.get("User-Agent"),
					ip: req.connection.remoteAddress,
					device: req.device.type,
					date: today
				});
				mUserSession.save(async function (err, storedData2) {
					if (err) { throw err; }
					res.json({
						code: 200,
						message: "Registration Successful.",
						data: {
							token: `${token}`,
							exp: decoded.exp,
							user_id: storedData2.user_id,
							email: storedData2.email,
							phone_number: storedData.phone_number,
							name: storedData2.name,
							is_active: storedData2.is_active
						}
					});
				});
			});
		}
	});
});

app.post("/loggined", upload.none(), async (req, res) => {
	const mToken = req.header("SESSION-TOKEN");
	await UserSession.findOne({ token: mToken, is_active: true }).then((resxx) => {
		if (resxx) {
			res.json({
				code: 200,
				data: { user: resxx },
				message: "Operation successful.",
			});
			return res.end();
		}
		else {
			res.json({
				code: 400,
				message: "User is logout",
			});
			return res.end();
		}
	});
});

app.post("/setlang", upload.none(), async (req, res) => {
	const mToken = req.header("SESSION-TOKEN");
	let temp = true;
	let ids = '';

	await UserSession.findOne({ token: mToken, is_active: true }).then(async (resxx) => {
		if (resxx) {
			await User.findByIdAndUpdate(
				resxx.user_id,
				{ language: req.body.lng },

				(err, updated) => {
					if (!err) {
						res.json({
							code: 200,
							data: { user: resxx },
							message: "Operation successful.",
						});
						return res.end();
					}

				}
			);

		}
		else {
			res.json({
				code: 400,
				message: "User is logout",
			});
			return res.end();
		}
	});
});

module.exports = {
    router: app
};
