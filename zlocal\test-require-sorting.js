// 测试 require 语句排序功能
// 这个文件故意打乱了 require 语句的顺序，用于测试 eslint-plugin-sort-requires

const lodash = require('lodash');
const path = require('path');
const { setupRoutes } = require('./Routes');
const moment = require('moment');
const fs = require('fs');
const express = require('express');
const http = require('http');
const BookHelper = require('./Helper/BookHelper');
const ramda = require('ramda');
const cors = require('cors');
const mongoose = require('mongoose');
const UserHelper = require('./Helper/UserHelper');
const axios = require('axios');

console.log('Testing require sorting...');

module.exports = {
    test: 'This is a test file for require sorting'
};
