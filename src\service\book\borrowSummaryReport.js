const moment = require('moment-timezone');
const Borrow = require('../../../Model/Borrow');
const Book = require('../../../Model/Book');
const User = require('../../../Model/User');
const Category = require('../../../Model/Category');
const _ = require('lodash');

// 解析日期范围
function parseDateRange(startDate, endDate) {
    return {
        start: moment.unix(startDate).tz('Asia/Hong_Kong').startOf('day').toDate(),
        end: moment.unix(endDate).tz('Asia/Hong_Kong').endOf('day').toDate(),
    };
}

// 获取借阅聚合数据
async function getBorrowSummaryRecords(start, end) {
    const baseMatch = {
        $or: [
            { issue_date: { $gte: start, $lte: end } },
            { reborrow_one_date: { $gte: start, $lte: end } },
            { reborrow_two_date: { $gte: start, $lte: end } }
        ]
    };
    const baseProject = {
        $project: {
            countWithRenewedCopies: {
                $switch: {
                    branches: [
                        {
                            case: { $and: [ { $eq: ["$reborrowed_once", false] }, { $eq: ["$reborrowed_twice", false] } ] },
                            then: 1
                        },
                        {
                            case: { $and: [ { $eq: ["$reborrowed_once", true] }, { $eq: ["$reborrowed_twice", false] } ] },
                            then: 2
                        },
                        {
                            case: { $and: [ { $eq: ["$reborrowed_once", true] }, { $eq: ["$reborrowed_twice", true] } ] },
                            then: 3
                        }
                    ],
                    default: 1
                }
            },
            user_id: "$user_id",
            book_id: "$book_id",
        }
    };
    // 按 user+book 分组
    const groupByBookUser = [
        { $match: baseMatch },
        baseProject,
        {
            $group: {
                _id: { book_id: "$book_id", user_id: "$user_id" },
                countWithRenewedCopies: { $sum: "$countWithRenewedCopies" },
                countWithoutRenewedCopies: { $sum: 1 }
            }
        },
        {
            $project: {
                book_id: "$_id.book_id",
                user_id: "$_id.user_id",
                countWithRenewedCopies: "$countWithRenewedCopies",
                countWithoutRenewedCopies: "$countWithoutRenewedCopies"
            }
        }
    ];
    // 按 book 分组
    const groupByBook = [
        { $match: baseMatch },
        baseProject,
        {
            $group: {
                _id: { book_id: "$book_id" },
                countWithRenewedCopies: { $sum: "$countWithRenewedCopies" },
                countWithoutRenewedCopies: { $sum: 1 }
            }
        },
        {
            $project: {
                book_id: "$_id.book_id",
                countWithRenewedCopies: "$countWithRenewedCopies",
                countWithoutRenewedCopies: "$countWithoutRenewedCopies"
            }
        }
    ];
    const [resultGroupByBookUser, resultGroupByBook] = await Promise.all([
        Borrow.aggregate(groupByBookUser),
        Borrow.aggregate(groupByBook)
    ]);
    return { resultGroupByBookUser, resultGroupByBook };
}

// 查询相关实体
async function fetchRelatedEntities(bookIds, userIds) {
    const [books, users, categories] = await Promise.all([
        Book.find({ _id: { $in: bookIds } }),
        User.find({ _id: { $in: userIds } }),
        Category.find()
    ]);
    return { books, users, categories };
}

// 映射聚合结果为业务对象
function mapSummaryItems(records, books, users, categories, groupByBook, collection_type) {
    return records.map(x => {
        const book = books.find(y => y._id.toString() === x.book_id.toString());
        if (collection_type && book.collection_type !== collection_type) return null;
        const user = users.find(y => y._id.toString() === x.user_id.toString());
        const matchedCategories = (book.category_id || []).map(y => {
            const category = categories.find(z => z._id.toString() === y.toString());
            return category ? category.name : '';
        });
        const subResultGroupByBook = groupByBook.find(y => y.book_id.toString() === x.book_id.toString());
        return {
            isbnNumber: book.isbn_no,
            bookName: book.title,
            publishGroup: book.publishingGroup,
            imprints: book.imprints,
            authorName: book.author,
            category: matchedCategories.join(','),
            borrowedCopiesGroupByUserWithRenewedCopies: x.countWithRenewedCopies,
            borrowedCopiesGroupByUserWithoutRenewedCopies: x.countWithoutRenewedCopies,
            borrowedCopiesGroupByBookWithRenewedCopies: subResultGroupByBook ? subResultGroupByBook.countWithRenewedCopies : 0,
            borrowedCopiesGroupByBookWithoutRenewedCopies: subResultGroupByBook ? subResultGroupByBook.countWithoutRenewedCopies : 0,
            patronId: user ? user.patronid : undefined
        };
    }).filter(x => !!x);
}

// 主入口
async function generateBorrowSummaryReport(startDate, endDate, collection_type) {
    const { start, end } = parseDateRange(startDate, endDate);
    const { resultGroupByBookUser, resultGroupByBook } = await getBorrowSummaryRecords(start, end);
    if (!resultGroupByBookUser || resultGroupByBookUser.length === 0) {
        return {
            borrowedCopies: 0,
            borrowedBooks: 0,
            renewedCopies: 0,
            renewedBooks: 0
        };
    }
    const bookIds = resultGroupByBook.map(x => x.book_id);
    const userIds = resultGroupByBookUser.map(x => x.user_id);
    const { books, users, categories } = await fetchRelatedEntities(bookIds, userIds);
    const items = mapSummaryItems(resultGroupByBookUser, books, users, categories, resultGroupByBook, collection_type);
    return {
        borrowedCopies: _.sum((items || []).map(x => x.borrowedCopiesGroupByUserWithRenewedCopies)),
        borrowedBooks: Array.from(new Set((items || []).map(x => x.isbnNumber))).length,
        renewedCopies: _.sum((items || []).map(x => x.borrowedCopiesGroupByUserWithRenewedCopies)) - _.sum((items || []).map(x => x.borrowedCopiesGroupByUserWithoutRenewedCopies)),
        renewedBooks: Array.from(new Set((items || []).filter(x => x.borrowedCopiesGroupByUserWithRenewedCopies > x.borrowedCopiesGroupByUserWithoutRenewedCopies).map(x => x.isbnNumber))).length,
    };
}

module.exports = { generateBorrowSummaryReport }; 