const app = require("express").Router();
const bcrypt = require("bcryptjs");
const { v4: uuidv4 } = require("uuid");
const AdminSession = require("../Model/AdminSession");
const Admin = require("../Model/Admin");
const Banner = require("../Model/Banner");
const Setting = require("../Model/Setting");
const jwt = require("jsonwebtoken");
const { AdminHelper } = require("../Helper/AdminHelper");
var mailer = require('../Model/Mail');
var path = require('path');
const fs = require("fs");

const multer = require("multer");
// const upload = multer();
app.use("/*", (req, res, next) => next());

const storage = multer.diskStorage({
	destination: function (req, file, cb) {
		cb(null, "uploads/Banner/");
	},
	filename: function (req, file, cb) {
		var extension = path.extname(file.originalname);
		let temp = file.originalname;
		temp = temp.split("#");
		temp = temp.join("_");
		cb(null, temp);
	},
	onError: function (err, next) {
		res.json({
			code: 422,
			sucess: false,
			message: err.message,
		});
	},
});

const fileFilter = (req, file, cb) => {
	if (
		file.mimetype === "image/jpeg" ||
		file.mimetype === "image/png" ||
		file.mimetype === "image/jpg"
	) {
		cb(null, true);
	} else {
		cb(null, false);
	}
};
const upload = multer({
	storage: storage,
	limits: {
		fileSize: 1024 * 1024 * 100,
	},
	fileFilter: fileFilter,
});


app.get("/settings", async (req, res) => {
	const collection_type = req.header("x-current-collection") || "JYG";

	const { key } = req.query;

	await Setting.findOne({ key, collection_type }).then(async (resx) => {
		if (resx) {
			res.json({
				code: 200,
				data: resx._doc,
				message: "Operation Successful",
			});
			return res.end();
		} else {
			res.json({
				code: 200,
				data: {
					key,
					collection_type,
					value: null,
				},
				message: "Setting not found",
			});
			return res.end();
		}
	});
});

app.post("/settings", AdminHelper, async (req, res) => {
	const collection_type = req.header("x-current-collection") || "JYG";

	const { key, value } = req.body;

	await Setting.findOne({ key, collection_type }).then(async (resx) => {
		if (resx) {
			await Setting.updateOne(
				{ key, collection_type },
				{ $set: { value } },
				{ upsert: false }
			);
		} else {
			await Setting.insertMany([{ key, value, collection_type }]);
		}
		res.json({
			code: 200,
			data: {
				key,
				value,
				collection_type,
			},
			message: "Operation Successful",
		});
		return res.end();
	});
});


app.post("/addNewAdmin", AdminHelper, async (req, res) => {
	const collection_type = req.header("x-current-collection") || "JYG";
	if (!req.body.email || !req.body.password || !req.body.name || !req.body.role) {
		res.status(422).json({
			code: 422,
			message: "Missing fields in request",
			success: false,
		});
		return res.end();
	}
	await Admin.findOne({ email: req.body.email, collection_type }).then(async (resx) => {
		if (resx) {
			res.json({
				code: 409,
				message: "Email already exists.",
				success: false,
			});
			return res.end();
		} else {
			const salt = await bcrypt.genSalt(10);
			const hash = await bcrypt.hash(req.body.password, salt);
			// 2 : librarian, 3: Publisher
			var super_admin = false; var role = false; var role = req.body.role;
			if (req.body.role == '1') { super_admin = true; }
			const mAdmin = Admin({
				name: req.body.name,
				email: req.body.email,
				phone_number: req.body.phone,
				password: hash,
				is_active: true,
				super_admin: super_admin,
				role: role,
				collection_type
			});
			await mAdmin
				.save()
				.then((storedData) => {
					res.json({
						code: 200,
						data: {
							name: storedData.name,
							email: storedData.email,
							is_active: true,
						},
						message: "Operation Successful",
					});
					return res.end();
				})
				.catch((err) => {
					res.json({
						code: 500,
						message: "Internal error in adding Member",
						success: false,
					});
				});
		}
	});
});

app.post("/update-password", async (req, res) => {
	const collection_type = req.header("x-current-collection") || "JYG";

	try {
		let user_id = req.body.user_id;
		let password = req.body.password;
		if (user_id && password) {
			await Admin.findOne({
				_id: user_id,
				collection_type
			}).then(async (resx) => {
				if (resx) {
					const salt = await bcrypt.genSalt(10);
					const hash = await bcrypt.hash(password, salt);
					resx.password = hash;
					resx.save();
					return res.json({
						code: 200,
						message: 'Password is Updated Successfully for ' + resx.email,
						success: true,
					});
				} else {
					return res.json({
						code: 500,
						message: "Something Went Wrong!",
						success: false,
					});
				}
			});
		}
	} catch (error) {
		console.log(error);
		return res.json({
			code: 500,
			message: "Internal error",
			success: false,
		});
	}
});

app.post("/reset-password", async (req, res) => {
	const collection_type = req.header("x-current-collection") || "JYG";

	try {
		let token = req.body.token;
		let password = req.body.password;
		if (token && password) {
			await Admin.findOne({
				reset_password_token: token,
				reset_password_expires: { $gt: Date.now() },
				collection_type
			}).then(async (resx) => {
				if (resx) {
					const salt = await bcrypt.genSalt(10);
					const hash = await bcrypt.hash(password, salt);
					resx.password = hash;
					resx.reset_password_token = undefined;
					resx.reset_password_expires = undefined;
					resx.save();

					mailOptions = {
						to: resx.email,
						from: '<EMAIL>',
						subject: 'Your Password is Updated Successfully',
						text: 'Hi, Your Password is Updated Successfully! You can login here ' + process.env.admin_base_url + ' to your account with your new password.'
					};
					mailer(mailOptions);

					return res.json({
						code: 200,
						message: 'Your Password is Updated Successfully',
						success: true,
					});
				} else {
					return res.json({
						code: 500,
						message: "Your Token Seems to be Expired! Please request again to reset your password.",
						success: false,
					});
				}
			});
		}
	} catch (error) {
		console.log(error);
		return res.json({
			code: 500,
			message: "Internal error",
			success: false,
		});
	}
});

app.post("/forgot", async (req, res) => {
	const collection_type = req.header("x-current-collection") || "JYG";
	try {
		if (req.body.email) {
			await Admin.findOne({ email: req.body.email, is_active: true, collection_type }).then((resx) => {
				if (resx) {
					var payload = {
						id: resx._id,        // User ID from database
						email: req.body.email
					};
					var secret = resx.email + '_' + new Date().getTime();
					var token = jwt.sign(payload, secret);

					Admin.findByIdAndUpdate({ _id: resx._id }, { reset_password_token: token, reset_password_expires: Date.now() + 86400000 }, { new: true }).exec(function (err, new_user) {
						console.log(process.env.admin_base_url + 'reset-password?token=' + token);
						mailOptions = {
							to: req.body.email,
							from: '<EMAIL>',
							subject: 'Reset Your Password',
							text: 'Hi, We have received your request to reset your password on ' + process.env.admin_base_url + '. Please click on the URL to Reset your password. URL : ' + process.env.admin_base_url + 'reset-password?token=' + token
						};
						mailer(mailOptions);
					});

					return res.json({
						code: 200,
						message: 'An e-mail has been sent to ' + req.body.email + '. Please check your email and click on the URL to reset your password.',
						success: true,
					});
				} else {
					return res.json({
						code: 500,
						message: "This Email Account is Not Exist",
						success: false,
					});
				}
			});
		} else {
			return res.json({
				code: 500,
				message: "Please Enter your email.",
				success: false,
			});
		}
	} catch (error) {
		console.log(error);
		return res.json({
			code: 500,
			message: "Internal error",
			success: false,
		});
	}
});

app.post("/get", AdminHelper, async (req, res) => {
	const collection_type = req.header("x-current-collection") || "JYG";
	try {
		const resx = await Admin.find({ collection_type });
		const temp = [];
		resx.map((el) => {
			temp.push({
				email: el.email,
				is_active: el.is_active,
				name: el.name,
				super_admin: el.super_admin,
				_id: el._id,
			});
		});
		return res.json({
			code: 200,
			message: "Operation successful",
			data: temp,
		});
	} catch (error) {
		return res.json({
			code: 500,
			message: "Internal error in Logout",
			success: false,
		});
	}
});

app.post("/login", async (req, res) => {
	const collection_type = req.header("x-current-collection") || "JYG";
	if (!req.body.email || !req.body.password) {
		res.status(422).json({
			code: 422,
			message: "Missing fields in request",
			success: false,
		});
		return res.end();
	}

	await Admin.findOne({ email: req.body.email, collection_type }).then((resx) => {
		if (!resx) {
			res.json({
				code: 409,
				message: "Email doesn't exists.",
				success: false,
			});
			return res.end();
		}
		bcrypt.compare(req.body.password, resx.password, (err, result) => {
			if (!result) {
				res.json({
					code: 409,
					message: "Invalid credential.",
					success: false,
				});
				return res.end();
			}
			jwt.sign(
				{
					id: uuidv4(),
				},
				process.env.JWT_SECRET,
				(err, token) => {
					if (err) console.log(err);
					const mAdminSession = new AdminSession({
						is_active: true,
						token: token,
						admin_id: resx._id,
						user_agent: req.get("User-Agent"),
						ip: req.connection.remoteAddress,
						device: req.device.type,
					});
					mAdminSession.save().then((storedData) => {
						res.json({
							code: 200,
							message: "Login Successful.",
							data: {
								_id: resx._id,
								role: resx.role,
								email: resx.email,
								token: storedData.token,
								super_admin: resx.super_admin,
							},
						});
						res.end();
					});
				}
			);
		});
	});
});

app.post("/logout", async (req, res) => {
	const collection_type = req.header("x-current-collection") || "JYG";

	if (!req.header("SESSION-TOKEN")) {
		res.json({
			code: 422,
			message: "Missing fields in request",
			success: false,
		});
		return res.end();
	}
	const updatedData = await AdminSession.findOneAndUpdate(
		{ token: req.header("SESSION-TOKEN"), collection_type },
		{ $set: { is_active: false } },
		(err, updated) => {
			if (err) {
				console.log(err);
				return res.json({
					code: 500,
					message: "Internal error in Logout",
					success: false,
				});
			}
			return res.json({
				code: 200,
				message: "Operation Successful",
				data: "",
			});
		}
	);
});

app.post("/addBanner",
	upload.fields([{
		name: "photo",
		maxCount: 1
	}
	]), AdminHelper, async function (req, res) {
		console.log(req.body.url);
		const collection_type = req.header("x-current-collection") || "JYG";
		if (req.body.bannerPhotoName && req.body.url) {
			const banner = await new Banner({ bannerimage: 'uploads/Banner/' + req.body.bannerPhotoName, url: req.body.url, collection_type }).save();
			if (banner)
				return res.json({ code: 200, message: "Banner updated successfully" });
		}
		return res.json({ code: 422, message: "Unable to update banner" });
	});

app.post("/updateBanner",
	upload.fields([{
		name: "photo",
		maxCount: 1
	}
	]), AdminHelper, async function (req, res) {
		const collection_type = req.header("x-current-collection") || "JYG";
		if (req.body.editID) {
			await Banner.findOne({
				_id: req.body.editID,
				collection_type
			}).then(async (resx) => {
				if (req.body.bannerPhotoName && req.body.url) {
					resx.bannerimage = 'uploads/Banner/' + req.body.bannerPhotoName;
					resx.url = req.body.url;
					resx.save();
					return res.json({ code: 200, message: "Banner updated successfully" });
				} else if (req.body.bannerPhotoName) {
					resx.bannerimage = 'uploads/Banner/' + req.body.bannerPhotoName;
					resx.save();
					return res.json({ code: 200, message: "Banner updated successfully" });
				} else if (req.body.url) {
					resx.url = req.body.url;
					resx.save();
					return res.json({ code: 200, message: "Banner updated successfully" });
				}
				return res.json({ code: 422, message: "Unable to update banner" });
			});
		}
	});

app.post("/getBannersListing", AdminHelper, async function (req, res) {
	const collection_type = req.header("x-current-collection") || "JYG";
	const banner = await Banner.find({ collection_type }).sort({ _id: -1 });
	if (banner)
		return res.json({ code: 200, data: banner, message: "" });

	return res.json({ code: 422, message: "Banners Not Available." });
});

app.post("/deleteBanner", AdminHelper, async function (req, res) {
	console.log(req.body);
	const collection_type = req.header("x-current-collection") || "JYG";
	if (req.body.delID) {
		await Banner.findOneAndDelete({
			_id: req.body.delID,
			collection_type
		}).then(async (resx) => {
			let bannerimage = resx && resx.bannerimage ? resx.bannerimage : '';
			if (fs.existsSync(bannerimage)) {
				fs.unlink(bannerimage, (err) => {
					if (err) {
						return res.json({
							code: 500,
							message: "Internal error at delete preview",
							success: false,
						});
					}
				});
			}

			return res.json({ code: 200, message: "Banner deleted successfully" });
		});
	}
	return res.json({ code: 422, message: "Banners Not Available." });
});

// app.post("/getRecentVideo", upload.none(), async function (req, res) {
// const video = await Video.findOne().sort({ _id: -1 });
// if (video)
// return res.json({ code: 200, url: video.url, message: "Url found" });

// return res.json({ code: 422, message: "Unable to fetch video" });
// });

module.exports = {
    router: app
};
