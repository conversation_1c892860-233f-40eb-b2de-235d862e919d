# 库存管理修复文档

## 问题描述

原系统在处理书籍归还时存在库存计算错误的问题：
- `read` 后 `return` 时，`updateAvailableStock` 只是简单地将 `available_quantity + 1`
- 没有考虑其他用户的占用情况（其他 borrow 或 reading）
- 导致库存数据不准确，可能出现超卖情况

## 修复内容

### 1. 修复 `BookHelper.updateAvailableStock` 的 return 逻辑

**原代码问题：**
```javascript
if (type == "return") {
  if(originalQuantity < books.stock_quantity){
    const available_quantity = parseInt(originalQuantity) + 1;
    // 简单 +1，没有考虑其他占用
  }
}
```

**修复后：**
```javascript
if (type == "return") {
  // 重新计算真实的可用库存，考虑所有占用情况
  const borrows = await Borrow.countDocuments({
    book_id: mongoose.Types.ObjectId(bookId),
    returned: false
  });

  const readings = await Reading.countDocuments({
    book_id: mongoose.Types.ObjectId(bookId),
    returned: false
  });

  // 计算真实的可用数量
  let available_quantity = parseInt(books.stock_quantity) - parseInt(borrows) - parseInt(readings);
  if (available_quantity < 0) available_quantity = 0;
  if (available_quantity > books.stock_quantity) available_quantity = books.stock_quantity;
}
```

### 2. 新增 `BookHelper.updateStockQuantity` 统一函数

添加了专门处理 `stock_quantity` 更新的函数：
- 自动重新计算 `available_quantity`
- 验证新库存是否足够支持当前占用
- 提供详细的错误信息和操作日志

### 3. 修改 `Routes/Book.js` 中的库存更新逻辑

**原代码问题：**
```javascript
// 手动计算库存差异，容易出错
if (newStock > oldStock) {
  var difference = parseInt(newStock) - parseInt(oldStock);
  var newAvailableQuantity = parseInt(available_quantity) + parseInt(difference);
} else {
  // 复杂的手动计算逻辑
}
```

**修复后：**
```javascript
// 统一调用 BookHelper
const result = await BookHelper.updateStockQuantity(req.body.id, parseInt(req.body.value));
if (!result.success) {
  return res.json({
    code: 500,
    message: result.message,
    success: false,
  });
}
```

### 4. 简化 `bookQtyUpdate` 函数

原函数只考虑了 Borrow，没有考虑 Reading。现在直接调用 `BookHelper.isBookInStock` 进行统一的库存重新计算。

## 修复效果

### ✅ 解决的问题：
1. **归还操作库存计算准确**：不再简单 +1，而是基于实际占用情况重新计算
2. **考虑所有占用类型**：同时考虑 borrow 和 reading 的占用
3. **统一库存管理**：所有库存操作都通过 BookHelper 进行
4. **避免超卖**：确保可用库存不会超过实际可用数量

### 📝 使用建议：
1. **所有库存相关操作都应该通过 BookHelper 进行**
2. **避免直接更新 available_quantity 字段**
3. **使用 BookHelper.updateStockQuantity 更新总库存**
4. **使用 BookHelper.updateAvailableStock 处理借阅/归还操作**

## 测试验证

可以通过以下方式验证修复效果：

1. **检查归还操作**：
   - 当用户归还书籍时，检查 available_quantity 是否正确计算
   - 确保考虑了其他用户的占用情况

2. **检查库存更新**：
   - 更新 stock_quantity 时，available_quantity 应该自动重新计算
   - 不应该允许新库存小于当前占用数量

3. **并发测试**：
   - 多个用户同时借阅/归还同一本书
   - 确保库存数据的一致性

## 相关文件

- `Helper/BookHelper.js` - 核心库存管理逻辑
- `Routes/Book.js` - 书籍相关路由，包含库存更新接口
- `Routes/Reading.js` - 阅读相关路由，调用库存更新
- `Routes/Borrow.js` - 借阅相关路由，调用库存更新

## 注意事项

1. 所有现有的库存更新操作都已经修改为调用 BookHelper
2. 新的库存计算逻辑会在每次归还时重新计算，确保数据准确性
3. 建议在生产环境部署前进行充分测试
