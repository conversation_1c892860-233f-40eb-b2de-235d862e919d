module.exports = {
    env: {
        es6: true,
        node: true,
        browser: true
    },
    extends: [
        'eslint:recommended'
    ],
    parserOptions: {
        ecmaVersion: 2020,
        sourceType: 'module'
    },
    rules: {
        // 检测未定义的变量
        'no-undef': 'error',
        // 检测未使用的变量
        'no-unused-vars': 'info',
        // 检测未声明的变量
        'no-undef-init': 'error'
    }
};