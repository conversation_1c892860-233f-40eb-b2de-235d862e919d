const mongoose = require("mongoose");
const async = require("async");
const _ = require("lodash");

const Admin = require("../../../Model/Admin");
const Book = require("../../../Model/Book");
const Borrow = require("../../../Model/Borrow");
const Reserve = require("../../../Model/Reserve");
const Preview = require("../../../Model/Preview");
const UserSession = require("../../../Model/UserSession");
const BookHelper = require("../../../Helper/BookHelper");

/**
 * 获取仪表板统计数据
 * @param {Object} params - 参数对象
 * @param {string} params.startDate - 开始日期
 * @param {string} params.endDate - 结束日期
 * @param {string} params.email - 用户邮箱
 * @param {string} params.collection_type - 集合类型
 * @returns {Object} 统计数据
 */
async function getDashboardCounts(params) {
    const { startDate, endDate, email, collection_type } = params;

    // 初始化统计变量
    let booksids = [];
    let noOfBorrowdBooks = 0, topbookscount = 0, noOfRenewalBooks = 0, Renbookscount = 0;
    let noOfpreviews = 0, previewcount = 0, totalBooks = 0, totalAvailableBooks = 0;
    let noOfRealTimeView = 0, noofrecommended = 0, noOfreservedBooks = 0, Resbookscount = 0;
    let noOfunused = 0, noofLoginsession = 0, loginsessioncount = 0;

    // 计算登录会话统计
    noofLoginsession = await UserSession.countDocuments({
        date: { $gte: new Date(startDate), $lte: new Date(endDate) },
    });
    
    const loginsessionUsers = await UserSession.distinct("user_id", {
        date: { $gte: new Date(startDate), $lte: new Date(endDate) },
    });
    loginsessioncount = loginsessionUsers.length;

    if (email) {
        const admin = await Admin.findOne({ email, collection_type });
        
        if (admin.role == '3') {
            // 管理员角色统计
            return await getAdminDashboardCounts({
                admin,
                startDate,
                endDate,
                collection_type,
                booksids,
                noOfBorrowdBooks, topbookscount, noOfRenewalBooks, Renbookscount,
                noOfpreviews, previewcount, totalBooks, totalAvailableBooks,
                noOfRealTimeView, noofrecommended, noOfreservedBooks, Resbookscount,
                noOfunused, noofLoginsession, loginsessioncount
            });
        } else {
            // 普通用户统计
            return await getRegularUserDashboardCounts({
                email,
                startDate,
                endDate,
                collection_type,
                booksids,
                noOfBorrowdBooks, topbookscount, noOfRenewalBooks, Renbookscount,
                noOfpreviews, previewcount, totalBooks, totalAvailableBooks,
                noOfRealTimeView, noofrecommended, noOfreservedBooks, Resbookscount,
                noOfunused, noofLoginsession, loginsessioncount
            });
        }
    }

    throw new Error("Email is required");
}

/**
 * 管理员仪表板统计
 */
async function getAdminDashboardCounts(params) {
    const {
        admin, startDate, endDate, collection_type, booksids,
        noOfBorrowdBooks, topbookscount, noOfRenewalBooks, Renbookscount,
        noOfpreviews, previewcount, totalBooks, totalAvailableBooks,
        noOfRealTimeView, noofrecommended, noOfreservedBooks, Resbookscount,
        noOfunused, noofLoginsession, loginsessioncount
    } = params;

    return new Promise((resolve, reject) => {
        async.series([
            // 借阅统计
            async function (callback) {
                const result = await getBorrowedBooksStats(admin._id, startDate, endDate, collection_type);
                Object.assign(params, result);
            },
            // 续借统计
            async function (callback) {
                const result = await getRenewedBooksStats(admin._id, startDate, endDate, collection_type);
                Object.assign(params, result);
            },
            // 预约统计
            async function (callback) {
                const result = await getReservedBooksStats(admin._id, startDate, endDate, collection_type);
                Object.assign(params, result);
            },
            // 书籍总数统计
            async function (callback) {
                const result = await getTotalBooksStats(admin._id, collection_type);
                Object.assign(params, result);
            },
            // 预览统计
            async function (callback) {
                const result = await getPreviewStats(admin._id, startDate, endDate, collection_type);
                Object.assign(params, result);
            },
            // 推荐书籍统计
            async function (callback) {
                const result = await getRecommendedBooksStats(admin._id, collection_type);
                Object.assign(params, result);
            }
        ], async function (err, results) {
            if (err) {
                return reject(err);
            }

            // 计算未使用书籍数量
            const unusedCount = await Book.countDocuments({
                collection_type,
                _id: { $nin: params.booksids },
                added_by: mongoose.Types.ObjectId(admin._id)
            });

            resolve({
                noOfRenewalBooks: params.noOfRenewalBooks,
                Renbookscount: params.Renbookscount,
                noOfRealTimeView: params.noOfRealTimeView,
                noOfBorrowdBooks: params.noOfBorrowdBooks,
                topbookscount: params.topbookscount,
                totalBooks: params.totalBooks,
                noofLoginsession: params.noofLoginsession,
                loginsessioncount: params.loginsessioncount,
                totalAvailableBooks: params.totalAvailableBooks,
                noofrecommended: params.noofrecommended,
                noOfpreviews: params.noOfpreviews,
                previewcount: params.previewcount,
                noOfreservedBooks: params.noOfreservedBooks,
                Resbookscount: params.Resbookscount,
                noOfunused: unusedCount,
            });
        });
    });
}

/**
 * 普通用户仪表板统计
 */
async function getRegularUserDashboardCounts(params) {
    const {
        email, startDate, endDate, collection_type, booksids,
        noOfBorrowdBooks, topbookscount, noOfRenewalBooks, Renbookscount,
        noOfpreviews, previewcount, totalBooks, totalAvailableBooks,
        noOfRealTimeView, noofrecommended, noOfreservedBooks, Resbookscount,
        noOfunused, noofLoginsession, loginsessioncount
    } = params;

    return new Promise((resolve, reject) => {
        async.series([
            // 续借统计（复杂逻辑）
            async function (callback) {
                const result = await getComplexRenewedBooksStats(startDate, endDate, collection_type);
                Object.assign(params, result);
            },
            // 预约统计
            async function (callback) {
                const result = await getGeneralReservedBooksStats(startDate, endDate, collection_type);
                Object.assign(params, result);
            },
            // 预览统计
            async function (callback) {
                const result = await getGeneralPreviewStats(email, startDate, endDate, collection_type);
                Object.assign(params, result);
            },
            // 借阅统计
            async function (callback) {
                const result = await getGeneralBorrowedBooksStats(startDate, endDate, collection_type);
                Object.assign(params, result);
            },
            // 书籍总数统计
            async function (callback) {
                const result = await getGeneralTotalBooksStats(collection_type);
                Object.assign(params, result);
            },
            // 推荐书籍统计
            async function (callback) {
                const result = await getGeneralRecommendedBooksStats(collection_type);
                Object.assign(params, result);
            }
        ], async function (err, results) {
            if (err) {
                return reject(err);
            }

            // 计算未使用书籍数量
            const unusedCount = await BookHelper.getunusedBookCountByDateV2(
                collection_type,
                startDate,
                endDate,
                email
            );

            resolve({
                noOfRenewalBooks: params.noOfRenewalBooks,
                Renbookscount: params.Renbookscount,
                noOfRealTimeView: params.noOfRealTimeView,
                noOfBorrowdBooks: params.noOfBorrowdBooks,
                topbookscount: params.topbookscount,
                totalBooks: params.totalBooks,
                noofLoginsession: params.noofLoginsession,
                loginsessioncount: params.loginsessioncount,
                totalAvailableBooks: params.totalAvailableBooks,
                noofrecommended: params.noofrecommended,
                noOfpreviews: params.noOfpreviews,
                previewcount: params.previewcount,
                noOfreservedBooks: params.noOfreservedBooks,
                Resbookscount: params.Resbookscount,
                noOfunused: unusedCount,
            });
        });
    });
}

/**
 * 获取借阅书籍统计（管理员）
 */
async function getBorrowedBooksStats(adminId, startDate, endDate, collection_type) {
    const result = await Borrow.aggregate([
        {
            $lookup: {
                from: "books",
                localField: "book_id",
                foreignField: "_id",
                as: "books",
            },
        },
        {
            $match: {
                "books.added_by": mongoose.Types.ObjectId(adminId),
                "books.collection_type": collection_type,
                issue_date: {
                    $gte: new Date(startDate),
                    $lte: new Date(endDate),
                },
            },
        },
        {
            $group: {
                _id: null,
                book_id: { $addToSet: "$book_id" },
                count: { $sum: 1 },
            },
        }
    ]);

    if (result.length) {
        const data = result[0];
        return {
            noOfBorrowdBooks: data.count,
            topbookscount: data.book_id.length,
            booksids: data.book_id
        };
    }
    return { noOfBorrowdBooks: 0, topbookscount: 0, booksids: [] };
}

/**
 * 获取续借书籍统计（管理员）
 */
async function getRenewedBooksStats(adminId, startDate, endDate, collection_type) {
    const result = await Borrow.aggregate([
        {
            $lookup: {
                from: 'books',
                localField: 'book_id',
                foreignField: '_id',
                as: 'books'
            }
        },
        {
            $match: {
                'books.added_by': mongoose.Types.ObjectId(adminId),
                'books.collection_type': collection_type,
                reborrowed_once: true,
                "$or": [
                    { reborrow_one_date: { "$gte": new Date(startDate), "$lte": new Date(endDate) } },
                    { reborrow_two_date: { "$gte": new Date(startDate), "$lte": new Date(endDate) } }
                ]
            }
        },
        {
            $group: { "_id": null, book_id: { $addToSet: "$book_id" }, count: { $sum: 1 } }
        }
    ]);

    if (result.length) {
        const data = result[0];
        return {
            noOfRenewalBooks: data.count,
            Renbookscount: data.book_id.length
        };
    }
    return { noOfRenewalBooks: 0, Renbookscount: 0 };
}

/**
 * 获取预约书籍统计（管理员）
 */
async function getReservedBooksStats(adminId, startDate, endDate, collection_type) {
    const result = await Reserve.aggregate([
        {
            $lookup: {
                from: 'books',
                localField: 'book_id',
                foreignField: '_id',
                as: 'books'
            }
        },
        {
            $match: {
                'books.added_by': mongoose.Types.ObjectId(adminId),
                'books.collection_type': collection_type,
                reserve_date: { "$gte": new Date(startDate), "$lte": new Date(endDate) }
            }
        },
        {
            $group: { "_id": null, book_id: { $addToSet: "$book_id" }, count: { $sum: 1 } }
        }
    ]);

    if (result.length) {
        const data = result[0];
        return {
            noOfreservedBooks: data.count,
            Resbookscount: data.book_id.length
        };
    }
    return { noOfreservedBooks: 0, Resbookscount: 0 };
}

/**
 * 获取书籍总数统计（管理员）
 */
async function getTotalBooksStats(adminId, collection_type) {
    const result = await Book.aggregate([
        {
            $match: {
                added_by: mongoose.Types.ObjectId(adminId),
                collection_type,
                $or: [{ is_deleted: { $exists: false } }, { is_deleted: { $exists: true, $eq: false } }],
            }
        },
        {
            $group: { "_id": null, totalBooks: { $sum: '$stock_quantity' }, totalAvailableBooks: { $sum: '$available_quantity' } }
        }
    ]);

    if (result.length) {
        const data = result[0];
        return {
            totalBooks: data.totalBooks,
            totalAvailableBooks: data.totalAvailableBooks
        };
    }
    return { totalBooks: 0, totalAvailableBooks: 0 };
}

/**
 * 获取预览统计（管理员）
 */
async function getPreviewStats(adminId, startDate, endDate, collection_type) {
    const result = await Preview.aggregate([
        {
            $lookup: {
                from: 'books',
                localField: 'book_id',
                foreignField: '_id',
                as: 'books'
            }
        },
        {
            $match: {
                'books.added_by': mongoose.Types.ObjectId(adminId),
                'books.collection_type': collection_type,
                "created_on": { "$gte": new Date(startDate), "$lte": new Date(endDate) }
            }
        },
        {
            $group: { "_id": null, book_id: { $addToSet: "$book_id" }, count: { $sum: 1 } }
        }
    ]);

    if (result.length) {
        const data = result[0];
        return {
            noOfpreviews: data.count,
            previewcount: data.book_id.length
        };
    }
    return { noOfpreviews: 0, previewcount: 0 };
}

/**
 * 获取推荐书籍统计（管理员）
 */
async function getRecommendedBooksStats(adminId, collection_type) {
    const count = await Book.countDocuments({
        added_by: mongoose.Types.ObjectId(adminId),
        collection_type,
        $or: [{ is_deleted: { $exists: false } }, { is_deleted: { $exists: true, $eq: false } }],
        book_recomm: true
    });
    return { noofrecommended: count };
}

/**
 * 获取复杂续借统计（普通用户）
 */
async function getComplexRenewedBooksStats(startDate, endDate, collection_type) {
    let first = 0;
    let second = 0;
    let firstBooks = [];
    let secondBooks = [];
    let booksCounts = [];

    // 第一次续借统计
    const firstResult = await Borrow.aggregate([
        {
            $lookup: {
                from: 'books',
                localField: 'book_id',
                foreignField: '_id',
                as: 'books'
            }
        },
        {
            $match: {
                'books.collection_type': collection_type,
                reborrowed_once: true,
                reborrow_one_date: { "$gte": new Date(startDate), "$lte": new Date(endDate) },
            }
        },
        {
            $group: {
                "_id": {
                    "book_id": "$book_id"
                },
                count: {
                    $sum: 1
                }
            }
        },
        {
            $group: {
                "_id": null,
                "data": {
                    "$push": {
                        "book_id": "$_id.book_id",
                        "count": "$count"
                    }
                }
            }
        }
    ]);

    if (firstResult.length > 0) {
        let book_ids = [];
        let fdata = firstResult[0].data;
        for (var j = 0; j < fdata.length; j++) {
            book_ids.push(fdata[j].book_id);
            first = parseInt(first) + fdata[j].count;
        }
        firstBooks = [...book_ids, ...firstBooks];
    }

    // 第二次续借统计
    const secondResult = await Borrow.aggregate([
        {
            $lookup: {
                from: 'books',
                localField: 'book_id',
                foreignField: '_id',
                as: 'books'
            }
        },
        {
            $match: {
                'books.collection_type': collection_type,
                reborrowed_twice: true,
                reborrow_two_date: { "$gte": new Date(startDate), "$lte": new Date(endDate) },
            }
        },
        {
            $group: {
                "_id": {
                    "book_id": "$book_id"
                },
                count: {
                    $sum: 1
                }
            }
        },
        {
            $group: {
                "_id": null,
                "data": {
                    "$push": {
                        "book_id": "$_id.book_id",
                        "count": "$count"
                    }
                }
            }
        }
    ]);

    if (secondResult.length > 0) {
        let sdata = secondResult[0].data;
        for (var j = 0; j < sdata.length; j++) {
            secondBooks.push(sdata[j].book_id);
            second = parseInt(second) + sdata[j].count;
        }
    }

    // 合并统计
    if (firstBooks.length > 0 && secondBooks.length > 0) {
        for (var i = 0; i < secondBooks.length; i++) {
            let isExist = false;
            for (var j = 0; j < firstBooks.length; j++) {
                if (typeof (booksCounts[j]) == 'undefined') {
                    booksCounts[j] = 1;
                }
                if (((secondBooks[i]).toString()).toLowerCase() == ((firstBooks[j].toString()).toLowerCase())) {
                    isExist = true;
                    booksCounts[j] = parseInt(booksCounts[j]) + 1;
                }
            }
            if (isExist == false) {
                firstBooks.push(secondBooks[i]);
            }
        }
    }

    const noOfRenewalBooks = parseInt(first) + parseInt(second);
    const Renbookscount = firstBooks.length;

    return {
        noOfRenewalBooks,
        Renbookscount,
        booksids: firstBooks
    };
}

/**
 * 获取预约书籍统计（普通用户）
 */
async function getGeneralReservedBooksStats(startDate, endDate, collection_type) {
    const result = await Reserve.aggregate([
        {
            $lookup: {
                from: 'books',
                localField: 'book_id',
                foreignField: '_id',
                as: 'books'
            }
        },
        {
            $match: { 'books.collection_type': collection_type, reserve_date: { "$gte": new Date(startDate), "$lte": new Date(endDate) } }
        },
        {
            $group: { "_id": null, book_id: { $addToSet: "$book_id" }, count: { $sum: 1 } }
        }
    ]);

    if (result.length) {
        const data = result[0];
        return {
            noOfreservedBooks: data.count,
            Resbookscount: data.book_id.length,
            booksids: data.book_id
        };
    }
    return { noOfreservedBooks: 0, Resbookscount: 0, booksids: [] };
}

/**
 * 获取预览统计（普通用户）
 */
async function getGeneralPreviewStats(email, startDate, endDate, collection_type) {
    const books = await BookHelper.previewBooksbydate('', new Date(startDate), new Date(endDate), '', email, collection_type);
    if (books) {
        const noOfpreviews = _.sum(books.map((x) => parseInt(x.counts)));
        const previewcount = books.filter(x => x.counts > 0).length;
        return { noOfpreviews, previewcount };
    }
    return { noOfpreviews: 0, previewcount: 0 };
}

/**
 * 获取借阅书籍统计（普通用户）
 */
async function getGeneralBorrowedBooksStats(startDate, endDate, collection_type) {
    const result = await Borrow.aggregate([
        {
            $lookup: {
                from: "books",
                localField: "book_id",
                foreignField: "_id",
                as: "books",
            },
        },
        {
            $match: {
                "books.collection_type": collection_type,
                issue_date: {
                    $gte: new Date(startDate),
                    $lte: new Date(endDate),
                },
            },
        },
        {
            $group: {
                _id: null,
                book_id: { $addToSet: "$book_id" },
                count: { $sum: 1 },
            },
        }
    ]);

    if (result.length) {
        const data = result[0];
        return {
            noOfBorrowdBooks: data.count,
            topbookscount: data.book_id.length,
            booksids: data.book_id
        };
    }
    return { noOfBorrowdBooks: 0, topbookscount: 0, booksids: [] };
}

/**
 * 获取书籍总数统计（普通用户）
 */
async function getGeneralTotalBooksStats(collection_type) {
    const result = await Book.aggregate([
        {
            $match: { collection_type, $or: [{ is_deleted: { $exists: false } }, { is_deleted: { $exists: true, $eq: false } }] }
        },
        {
            $group: { "_id": null, totalBooks: { $sum: '$stock_quantity' }, totalAvailableBooks: { $sum: '$available_quantity' } }
        }
    ]);

    if (result.length) {
        const data = result[0];
        return {
            totalBooks: data.totalBooks,
            totalAvailableBooks: data.totalAvailableBooks
        };
    }
    return { totalBooks: 0, totalAvailableBooks: 0 };
}

/**
 * 获取推荐书籍统计（普通用户）
 */
async function getGeneralRecommendedBooksStats(collection_type) {
    const count = await Book.countDocuments({
        collection_type,
        $or: [{ is_deleted: { $exists: false } }, { is_deleted: { $exists: true, $eq: false } }],
        book_recomm: true
    });
    return { noofrecommended: count };
}

module.exports = {
    getDashboardCounts
};
