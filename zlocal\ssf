{ provide: ReadingReflectionServiceAbstract, useValue: null },

modules 里全面改吧。如果注入的不是全局模块的provider，如果是业务模块的provider，看看都注入成 shared module 下 的 interfaces 下 的 xxabstract.ts。太多了，希望你能弄一下。因为这样比较中介者模式，不同几个模块相互串来串去。我一下就能看到 account, schools, assistant, books modules 间还是有相互依赖。用 modules/xx 来排查存在，如果在 constructor 里用到了，那就是要改。像 account module 的 user client controller, user public controller 就用到了 assistantcontractsService。像 assistant module 又用到了 school 下的 grade service。完善 shared module，完善引用。{ provide: xxAbstract, useValue: null }, { provide: xxAbstract, useClass: xxService }。尽量扫描全和修改全

{ _id: ObjectId("6103fe5f18271b1fc98e4fe2") }

