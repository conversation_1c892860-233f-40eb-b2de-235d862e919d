const User = require("../Model/UserSession");

async function UserHelper(req, res, next) {
	const SESSION_TOKEN = req.header("SESSION-TOKEN");

	if (!SESSION_TOKEN) {
		res.json({ code: 900, success: false, message: "Invalid request." });
		return res.end();
	}

	await User.findOne({ token: SESSION_TOKEN })
		.then((resx) => {
			if (!resx) {
				res.json({
					code: 900,
					success: false,
					message: "Invalid request.",
				});
				return res.end();
			}
			return next();
		})
		.catch((err) => {
			console.log(err);
			res.json({
				code: 500,
				message: "Internal error in header check",
				success: false,
			});
			return res.end();
		});
}

module.exports.UserHelper = UserHelper;
