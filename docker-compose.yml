version: "3.4"

services:
  mongo:
    image: mongo:4.4.0
    ports:
      - 27017:27017
    environment:
      MONGO_INITDB_ROOT_USERNAME: service
      MONGO_INITDB_ROOT_PASSWORD: b5f56b5df5ea1e5682dda6c1a283c91f
      MONGO_INITDB_DATABASE: ebook
    volumes:
      - ./docker-entrypoint-initdb/mongo.init.js:/docker-entrypoint-initdb.d/mongo.init.js:ro
    networks:
      - traefik-net

networks:
  traefik-net:
    external: true
