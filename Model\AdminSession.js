const mongoose = require("mongoose");
const Schema = mongoose.Schema;

const AdminSession = new Schema({
	admin_id: {
		required: true,
		type: Schema.Types.ObjectId,
		ref: "Admin",
	},
	token: {
		required: true,
		type: String,
	},
	is_active: {
		required: true,
		type: Boolean,
	},
	ip: {
		required: true,
		type: String,
	},
	user_agent: {
		required: true,
		type: String,
	},
	device: {
		required: true,
		type: String,
	},
});

module.exports = mongoose.model("AdminSession", AdminSession);
