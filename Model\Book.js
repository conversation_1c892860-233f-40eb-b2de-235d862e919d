const mongoose = require("mongoose");
const Schema = mongoose.Schema;

const Book = new Schema({
	category_id: [
		{
			type: Schema.Types.ObjectId,
			ref: "Category",
		},
	],
	title: {
		required: true,
		type: String,
	},
	excerpt: {
		required: true,
		type: String,
	},
	stock_quantity: {
		required: true,
		type: Number,
	},
	available_quantity: {
		type: Number,
	},
	publishingGroup: {
		type: String,
	},
	imprints: {
		type: String,
	},
	author: {
		required: true,
		type: String,
	},
	total_pages: {
		required: false,
		type: Number,
	},
	cost: {
		required: false,
		type: String,
	},
	cover_photo: {
		required: true,
		type: String,
	},
	book_pdf: {
		required: true,
		type: String,
	},
	book_recomm : {
		required: false,
		type:Boolean,
		default: false,
	},
	book_recomm_datetime : {
		type: Date,
		required: false
	},
	preview_book: {
		required: false,
		type: String,
	},
	added_by: {
		type: Schema.Types.ObjectId,
		ref: "Admin",
		required: true,
	},
	added_at: {
		type: Date,
		required: true,
	},
	publish_date :{
		type: Date,
		required: false,
	},
	isbn_no: {
		type: String,
		required: true
	},
	is_deleted: {
		required: false,
		type:Boolean,
		default: false,
	},
	deleted_at: {
		required: false,
		type: Date,
	},
    collection_type: {
		type: String,
		required: false,
		default:'JYG'
	},
});
module.exports = mongoose.model("Book", Book);