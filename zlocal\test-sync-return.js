const mongoose = require("mongoose");
require("dotenv").config();

// 连接数据库
mongoose.connect(process.env.DB_URL, { 
    useUnifiedTopology: true, 
    useNewUrlParser: true, 
    useCreateIndex: true, 
    useFindAndModify: false 
}).then((db) => {
    console.log("MongoDB connected");
    testSyncReturnBooks();
}).catch((error) => console.log("Lost Connection : " + error));

// 导入需要的模型和函数
const { syncReturnBooks } = require('./Routes/Borrow');
const Reading = require('./Model/Reading');
const Reserve = require('./Model/Reserve');
const Book = require('./Model/Book');
const User = require('./Model/User');

async function testSyncReturnBooks() {
    try {
        console.log('\n=== 开始测试 syncReturnBooks 函数 ===\n');
        
        // 1. 查找一些测试数据
        console.log('1. 查找测试数据...');
        
        // 查找一些书籍
        const books = await Book.find({ is_deleted: { $ne: true } }).limit(3);
        console.log('找到书籍数量:', books.length);
        
        if (books.length > 0) {
            for (let book of books) {
                console.log(`书籍: ${book.title} (ID: ${book._id})`);
                
                // 查找这本书的预约记录
                const reserves = await Reserve.find({ 
                    book_id: book._id, 
                    is_deleted: false 
                });
                console.log(`  - 预约记录数量: ${reserves.length}`);
                
                if (reserves.length > 0) {
                    console.log(`  - 测试 syncReturnBooks 函数，book_id: ${book._id}`);
                    await syncReturnBooks(book._id);
                    console.log(`  - syncReturnBooks 执行完成`);
                    break; // 只测试第一本有预约的书
                }
            }
        }
        
        // 2. 查找一些阅读记录
        console.log('\n2. 查找阅读记录...');
        const readings = await Reading.find({ returned: false }).limit(5);
        console.log('找到未归还的阅读记录数量:', readings.length);
        
        if (readings.length > 0) {
            const reading = readings[0];
            console.log(`测试阅读记录: ${reading._id}`);
            console.log(`书籍ID: ${reading.book_id}`);
            
            // 测试调用 syncReturnBooks
            console.log('调用 syncReturnBooks...');
            await syncReturnBooks(reading.book_id);
            console.log('syncReturnBooks 调用完成');
        }
        
        console.log('\n=== 测试完成 ===');
        
    } catch (error) {
        console.error('测试过程中出现错误:', error);
    } finally {
        // 关闭数据库连接
        setTimeout(() => {
            mongoose.connection.close();
            process.exit(0);
        }, 2000);
    }
}
