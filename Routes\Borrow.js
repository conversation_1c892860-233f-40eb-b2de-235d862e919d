const app = require("express").Router();
const {
    UserHelper
} = require("../Helper/UserHelper");
const Borrow = require("../Model/Borrow");
const { batchReturnBorrowedBooks } = require("../src/action/borrowAction");
const { syncReturnBooks } = require("../src/action/bookActions");
const Book = require("../Model/Book");
const Reserve = require("../Model/Reserve");
const multer = require("multer");
const UserSession = require("../Model/UserSession");
const upload = multer();
const BookHelper = require("../Helper/BookHelper");
var mailer = require('../Model/Mail');
const User = require("../Model/User");
var mongoose = require('mongoose');
const SystemLog = require("../Model/SystemLog");
const { MAX_BORROW_BOOK_NUM, MAX_BORROW_DAYS } = require("../consts");

// 内存缓存
const cache = {};
// 设置缓存过期时间（单位：毫秒）
const CACHE_EXPIRY_TIME = 60000; // 1分钟



app.get("/auto-logout-user", async (req, res) => {
    console.log('every 5 min');
    let loggedin_users = [];
    await UserSession.find({ is_active: true }).then(async (resp) => {
        resp.forEach(async (resp_el) => {
            var respdatetodate = new Date(resp_el.timestamp);
            var todaydate = new Date();
            respdatetodate.setMinutes(respdatetodate.getMinutes() + 60);
            if (todaydate > respdatetodate) {
                await UserSession.updateOne({
                    _id: mongoose.Types.ObjectId(resp_el._id)
                }, {
                    $set: {
                        is_active: false
                    }
                }, function (err, res) {
                    if (err) throw err;
                    loggedin_users.push(resp_el);
                });
            }
        });
    });

    return res.send({
        code: 200,
        data: loggedin_users,
        message: "User will auto-logout after 60 min of login time."
    });
});

app.get("/sync-return-books", async (req, res) => {
    let message = "book return cron will run every day at 12:00 AM on " + new Date();
    var output = '';
    await syncReturnBooks();

    console.log({ code: 200, data: output, message: message });
    return res.send({
        code: 200,
        data: output,
        message: message
    });
});

app.use("/*", (req, res, next) => next());

app.post("/book", upload.none(), UserHelper, async (req, res) => {
    if (
        !req.body.user_id ||
        !req.body.book_id ||
        !req.body.issue_date ||
        !req.body.return_date ||
        !req.body.status
    ) {
        return res.json({
            code: 418,
            message: "Missing required fields",
            status: false,
        });
    }

    // 同一用户同一本书只能借阅一次
    var cacheKey = 'cache_' + req.body.user_id + '_' + req.body.book_id;
    if (cache[cacheKey]) {
        // 存在，表示处理中，直接返回
        return res.json({
            code: 418,
            message: "Processing in progress",
            status: false,
        });
    }
    cache[cacheKey] = cacheKey;

    const _user = await User.findOne({
        _id: new mongoose.Types.ObjectId(req.body.user_id)
    })
    if (!_user) {
        const systemLog = SystemLog({
            ctime: new Date(),
            data: {
                body: req.body,
                token: req.header("SESSION-TOKEN")
            }
        })
        await systemLog.save()
    }

    const book = await Book.findOne({ _id: new mongoose.Types.ObjectId(req.body.book_id) })

    const borrowLogs = await Borrow.find({
        book_id: mongoose.Types.ObjectId(book._id),
        user_id: mongoose.Types.ObjectId(_user._id),
        returned: false,
        is_deleted: false
    })
    if (borrowLogs && borrowLogs.length > 0) {
        res.json({
            code: 420,
            message: "You've borrowed this book",
            status: false,
        });
        // 删除缓存
        delete cache[cacheKey];
        return res.end();
    }

    const issue_date = req.body.issue_date;//moment.unix(req.body.issue_date).tz('Asia/Hong_Kong').toDate();//.startOf('day').toDate();
	const return_date = req.body.return_date;//moment.unix(req.body.return_date).tz('Asia/Hong_Kong').toDate();//.endOf('day').toDate();

    var valid = true;
    // var issue_date = new Date();
    //issue_date.setHours(0, 0, 0, 0);
    // const maxReturnDays = MAX_BORROW_DAYS; // 最大可借阅天数
    const maxBorrowBooks = MAX_BORROW_BOOK_NUM; // 最大可借阅本书
    /*var date = new Date();
    //date.setHours(0, 0, 0, 0);
    date.setDate(date.getDate() + maxReturnDays);
    date.setHours(23, 59, 0, 0);
    const return_date = date;*/
    if (valid == true) {
        await User.find({
            _id: {
                $ne: req.body.user_id
            }
        }).then(async (resx) => {
            Borrow.find({
                user_id: mongoose.Types.ObjectId(req.body.user_id)
            }).then(async (resx) => {

                if (await BookHelper.totalBookBorrowedInPresent(req.body.user_id, book.collection_type) >= maxBorrowBooks) {
                    res.json({
                        code: 420,
                        message: "You've reached maximum limit of borrow!",
                        status: false,
                    });
                    // 删除缓存
                    delete cache[cacheKey];
                    return res.end();
                } else if (await BookHelper.isBookBorrowed(req.body.user_id, req.body.book_id)) {
                    res.json({
                        code: 421,
                        message: "You've already borrowed this!",
                        status: false,
                    });
                    // 删除缓存
                    delete cache[cacheKey];
                    return res.end();
                } else if (!(await BookHelper.isBookInStock(req.body.book_id))) {
                    // 删除缓存
                    delete cache[cacheKey];
                    return res.json({
                        code: 422,
                        message: "This book is out of stock!",
                        status: false,
                    });
                } else {
                    var user_id = req.body.user_id;
                    var book_id = req.body.book_id;

                    const mBorrow = Borrow({
                        email: req.body.email,
                        user_id: req.body.user_id,
                        book_id: req.body.book_id,
                        issue_date: issue_date,
                        return_date: return_date,
                        returned: req.body.status,
                        is_deleted: false
                    });

                    mBorrow
                        .save()
                        .then((stored) => {
                            (BookHelper.updateAvailableStock(book_id, 'borrow'));
                            Reserve.findOneAndUpdate({
                                book_id: req.body.book_id,
                                is_deleted: false,
                                user_id: req.body.user_id
                            }, {
                                is_deleted: true
                            }, {
                                useFindAndModify: false
                            },
                                (err, updated) => { });
                            User.findOne({
                                _id: user_id
                            }).then((userData) => {
                                if (userData) {
                                    var email = req.body.email;
                                    var name = userData.name;
                                    Book.findOne({
                                        _id: book_id
                                    }).then((bookData) => {

                                        if (1) {
                                            var dateObj = req.body.return_date;
                                            var dateArr = dateObj.split(' ');
                                            var return_date = dateArr[0] + ', ' + dateArr[1] + ' ' + dateArr[2] + ' ' + dateArr[3];
                                            mailOptions = {
                                                to: email,
                                                from: '<EMAIL>',
                                                subject: 'Book Borrowed : Enrich Culture Group',
                                                text: 'Hi! You Have Borrow this Book "' + bookData.title + '" which will return automatically on Return Date : ' + return_date
                                            };
                                            //mailer(mailOptions);
                                            // 删除缓存
                                            delete cache[cacheKey];
                                            return res.json({
                                                code: 200,
                                                data: stored,
                                                message: "Operation successful.",
                                            });
                                        } else {
                                            // 删除缓存
                                            delete cache[cacheKey];
                                            return res.json({
                                                code: 500,
                                                data: stored,
                                                message: "Internal Error",
                                                status: false,

                                            });
                                        }
                                    });
                                }
                            });
                        })
                        .catch((err) => {
                            // 删除缓存
                            delete cache[cacheKey];
                            return res.json({
                                code: 500,
                                message: "Internal Error in borrowing.",
                                status: false,
                            });
                        });
                }
            });
        });
    } else {
        // 删除缓存
        delete cache[cacheKey];
        return res.json({
            code: 424,
            message: "Enter valid Email-id",
            status: false,
        });
    }
});

app.post("/return", upload.none(), UserHelper, async (req, res) => {
    if (!req.body.borrow_id) {
        return res.json({
            code: 422,
            message: "Missing required fields",
            status: false,
        });
    }
    await Borrow.findOne({
        _id: req.body.borrow_id
    }).then(async (resx) => {
        if (!resx) {
            return res.json({
                code: 422,
                message: "No data found",
                status: false,
            });
        } else {
            var user_id = resx.user_id;
            var book_id = resx.book_id;

            (BookHelper.updateAvailableStock(book_id, 'return'));
            await Reserve.updateOne({
                user_id: user_id,
                is_deleted: false
            }, {
                $set: {
                    is_blocked: false
                }
            },
                (err, updated) => { });

            User.findOne({
                _id: user_id
            }).then((userData) => {
                if (userData) {
                    var email = userData.email;
                    var name = userData.name;
                    Book.findOne({
                        _id: book_id
                    }).then(async (bookData) => {
                        if (email) {
                            mailOptions = {
                                to: email,
                                from: '<EMAIL>',
                                subject: 'Book Returned : Enrich Culture Group',
                                text: 'Hi, Your Borrowed Book "' + bookData.title + '" is returned Succesfully.'
                            };
                            // mailer(mailOptions);

                            /* When return the book, it should check weather this book is deleted and if it's borrowed from someone else or not, if its not then it should be deleted otherwise it will remain as a tease */
                            let preview_book = bookData.preview_book ? bookData.preview_book : '';
                            let book_pdf = bookData.book_pdf ? bookData.book_pdf : '';
                            let cover_photo = bookData.cover_photo ? bookData.cover_photo : '';
                            let book_deleted = bookData.is_deleted ? bookData.is_deleted : '';

                            var countsborrow = await Borrow.find({ book_id: mongoose.Types.ObjectId(book_id), is_deleted: false, returned: false });
                            if (countsborrow.length == 0 && book_deleted == true) {
                                if (fs.existsSync(preview_book)) {
                                    fs.unlink(preview_book, (err) => {
                                        if (err) {
                                            return res.json({
                                                code: 500,
                                                message: "Internal error at delete preview",
                                                success: false,
                                            });
                                        }
                                    });
                                }
                                if (fs.existsSync(book_pdf)) {
                                    fs.unlink(book_pdf, (err) => {
                                        if (err) {
                                            return res.json({
                                                code: 500,
                                                message: "Internal error at delete book pdf",
                                                success: false,
                                            });
                                        }
                                    });
                                }
                                if (fs.existsSync(cover_photo)) {
                                    fs.unlink(cover_photo, (err) => {
                                        if (err) {
                                            return res.json({
                                                code: 500,
                                                message: "Internal error at delete cover",
                                                success: false,
                                            });
                                        }
                                    });
                                }
                            }

                        }
                    });
                }
            });

            /* Auto borrow the reserved book */
            await syncReturnBooks(book_id);

        }
    });

    await Borrow.findOneAndUpdate({
        _id: req.body.borrow_id
    }, {
        returned: true,
        return_date: new Date()
    }, {
        useFindAndModify: false
    },
        (err, updated) => {
            return res.json({
                code: 200,
                message: "Operation Succesful",
                status: true,
            });
        }
    ).catch((err) => {
        return res.json({
            code: 500,
            message: "Internal error in update book",
            status: true,
        });
    });
});

app.post("/deleteBook", upload.none(), UserHelper, async (req, res) => {
    if (!req.body.delete_id) {
        return res.json({
            code: 422,
            message: "Missing required fields",
            status: false,
        });
    }
    let doc = await Borrow.findOneAndUpdate({
        _id: req.body.delete_id
    }, {
        is_deleted: true
    });

    return res.json({
        code: 200,
        data: false,
        message: "Operation successful.",
    });
});
app.post("/alldeleteBook", upload.none(), UserHelper, async (req, res) => {
    if (!req.body.delete_id) {
        return res.json({
            code: 422,
            message: "Missing required fields",
            status: false,
        });
    }
    var bookbulk = [];
    const arr = req.body.delete_id.split(",");
    const promises1 = arr.map(async (obj) => {
        bookbulk.push(obj);
    });

    const results1 = await Promise.all(promises1);
    const options = {
        ordered: true
    };
    var query = {
        _id: bookbulk
    };
    var data = {
        $set: {
            is_deleted: true
        }
    }
    const result = await Borrow.updateMany(query, data);
    return res.json({
        code: 200,
        data: req.body.delete_id,
        message: "Operation successful.",
    });
});

app.post("/renew", upload.none(), UserHelper, async (req, res) => {

    if (!req.body.borrow_id) {
        return res.json({
            code: 422,
            message: "Missing required fields",
            status: false,
        });
    }

    await Borrow.aggregate([
        {
            $match: { _id: mongoose.Types.ObjectId(req.body.borrow_id) }
        },
        {
            $lookup: {
                from: 'books',
                localField: 'book_id',
                foreignField: '_id',
                as: 'books'
            }
        },
        {
            $group: { _id: null, book_deleted: { $first: "$books.is_deleted" } }
        }
    ], async function (err, result) {
        console.log('result = ', result);
        if (result) {
            book_deleted = result[0].book_deleted;
            console.log(book_deleted);
            if (book_deleted) {
                return res.json({
                    code: 422,
                    message: "This book is no longer available with Hong Kong Public Library, You can't renew this book.",
                    status: false,
                });
            }
        }
    });


    if (!(await BookHelper.isReBorrowAllowed(req.body.borrow_id))) {
        return res.json({
            code: 422,
            message: "Renewal of this book is not allowed",
            status: false,
        });
    }

    const borrow = await Borrow.findOne({
        _id: mongoose.Types.ObjectId(req.body.borrow_id)
    });

    if (!borrow) {
        res.json({
            code: 422,
            message: "No data found",
            status: false,
        });
        return res.end();
    }

    // 同一用户同一本书只能续阅一次
    const cacheKey = 'cache_renew' + req.body.user_id + '_' + req.body.book_id;

    if (cache[cacheKey] && cache[cacheKey].expiry > Date.now()) {
        // 存在且未过期，表示处理中，直接返回
        return res.json({
            code: 418,
            message: "Processing in progress",
            status: false,
        });
    }

    // 添加到缓存并设置过期时间
    cache[cacheKey] = {
        value: cacheKey,
        expiry: Date.now() + CACHE_EXPIRY_TIME,
    };

    // 清理过期的缓存
    setTimeout(() => {
        if (cache[cacheKey] && cache[cacheKey].expiry <= Date.now()) {
            delete cache[cacheKey];
        }
    }, CACHE_EXPIRY_TIME);

    var mailMessage = '';
    var issue_date = borrow.issue_date;
    let currentReturnDate = borrow.return_date;
    if (!borrow.reborrowed_once) {
        let newReturnDate = new Date(currentReturnDate);
        newReturnDate.setDate(newReturnDate.getDate() + MAX_BORROW_DAYS);
        // newReturnDate.setHours(23, 59, 0, 0);
        //newReturnDate.setDate(newReturnDate.getDate() + 7);
        let doc = await Borrow.findOneAndUpdate({
            _id: req.body.borrow_id
        }, {
            reborrowed_once: true,
            reborrow_one_date: new Date(),
            return_date: newReturnDate,
        });
        User.findOne({
            _id: borrow.user_id
        }).then((userData) => {
            if (userData) {
                var email = userData.email;
                var name = userData.name;
                Book.findOne({
                    _id: borrow.book_id
                }).then((bookData) => {
                    if (email) {
                        var dateObj = newReturnDate.toString();
                        var dateArr = dateObj.split(' ');
                        var return_date = dateArr[0] + ', ' + dateArr[1] + ' ' + dateArr[2] + ' ' + dateArr[3];
                        mailOptions = {
                            to: email,
                            from: '<EMAIL>',
                            subject: 'Book Renewed : Enrich Culture Group',
                            text: 'Hi, Your Borrowed Book "' + bookData.title + '" is Renewed Succesfully. Youe new Return Date is ' + return_date
                        };
                        //mailer(mailOptions);
                    }
                });
            }
        });

        return res.json({
            code: 200,
            data: {
                new_return_date: newReturnDate,
                issue_date: issue_date,
                reborrowed_once: true,
                reborrowed_twice: false,
            },
            message: "Operation successful.",
        });
    } else {
        if (!borrow.reborrowed_twice) {
            let newReturnDate = new Date(currentReturnDate);
            newReturnDate.setDate(newReturnDate.getDate() + MAX_BORROW_DAYS);
            // let newReturnDate = new Date(borrow.issue_date);
            // newReturnDate.setDate(newReturnDate.getDate() + 7);
            // newReturnDate.setDate(newReturnDate.getDate() + MAX_BORROW_DAYS * 3);
            // newReturnDate.setHours(23, 59, 0, 0);
            let doc = await Borrow.findOneAndUpdate({
                _id: req.body.borrow_id
            }, {
                reborrowed_twice: true,
                reborrow_two_date: new Date(),
                return_date: newReturnDate,
            });
            User.findOne({
                _id: borrow.user_id
            }).then((userData) => {
                if (userData) {
                    var email = userData.email;
                    var name = userData.name;
                    Book.findOne({
                        _id: borrow.book_id
                    }).then((bookData) => {
                        if (email) {
                            var dateObj = newReturnDate.toString();
                            var dateArr = dateObj.split(' ');
                            var return_date = dateArr[0] + ', ' + dateArr[1] + ' ' + dateArr[2] + ' ' + dateArr[3];
                            mailOptions = {
                                to: email,
                                from: '<EMAIL>',
                                subject: 'Book Renewed : Enrich Culture Group',
                                text: 'Hi, Your Borrowed Book "' + bookData.title + '" is Renewed Succesfully. Youe new Return Date is ' + return_date
                            };
                            //mailer(mailOptions);
                        }
                    });
                }
            });

            return res.json({
                code: 200,
                data: {
                    new_return_date: newReturnDate,
                    issue_date: issue_date,
                    reborrowed_once: true,
                    reborrowed_twice: true,
                },
                message: "Operation successful.",
            });
        } else {
            return res.json({
                code: 200,
                message: "You can only Renew a book 2 times",
                status: false,
            });
        }
    }

});

app.post("/returndetail", upload.none(), UserHelper, async (req, res) => {
    if (!req.body.user_id) {
        return res.json({
            code: 422,
            message: "Missing required fields",
            status: false,
        });
    }

    let borrows = await Borrow.find({
        user_id: req.body.user_id,
        is_deleted: false,
        returned: true
    }).sort({
        _id: -1
    }).populate(
        "book_id"
    );

    if (!borrows) {
        res.json({
            code: 422,
            message: "You've reached maximum limit of borrow!",
            status: false,
        });
        return res.end();
    }

    const response = [];
    for (let borrowData of borrows) {
        if (borrowData.book_id) {
            const data = {
                ...borrowData._doc
            };
            data.is_reborrow_allowed = await BookHelper.isReBorrowAllowed(borrowData._id);
            response.push(data);
        }
    }

    res.json({
        code: 200,
        message: "Operation successful.",
        data: response,
    });
    return res.end();
});
app.post("/detail", upload.none(), UserHelper, async (req, res) => {
    if (!req.body.user_id) {
        return res.json({
            code: 422,
            message: "Missing required fields",
            status: false,
        });
    }

    let borrows = await Borrow.find({
        user_id: req.body.user_id,
        is_deleted: false,
        returned: false
    }).sort({
        _id: -1
    }).populate(
        "book_id"
    );

    if (!borrows) {
        res.json({
            code: 422,
            message: "You've reached maximum limit of borrow!",
            status: false,
        });
        return res.end();
    }

    const response = [];
    for (let borrowData of borrows) {
        if (borrowData.book_id) {
            const data = {
                ...borrowData._doc
            };
            data.is_reborrow_allowed = await BookHelper.isReBorrowAllowed(borrowData._id);
            response.push(data);
        }
    }

    res.json({
        code: 200,
        message: "Operation successful.",
        data: response,
    });
    return res.end();
});

// Moved to src/action/borrowAction.js

app.post("/batchReturnBorrowedBoooks", async (req, res) => {
    try {
        const condition = {
            is_deleted: false,
            returned: false,
            return_date: {
                $lt: new Date()
            }
        };
        
        if (req.body.user_id) {
            Object.assign(condition, { user_id: req.body.user_id });
        }

        // Process batch returns
        await batchReturnBorrowedBooks(condition);

        res.json({
            code: 200,
            message: "Operation successful.",
            data: { ...condition },
        });
    } catch (error) {
        console.error("Error in batchReturnBorrowedBooks:", error);
        res.status(500).json({
            code: 500,
            message: "Internal server error during batch return.",
            error: error.message
        });
    }
});


module.exports = {
    router: app,
    syncReturnBooks
};
