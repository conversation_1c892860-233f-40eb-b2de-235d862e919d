const app = require("express").Router();
const mongoose = require('mongoose');
const multer = require("multer");

const Reading = require("../Model/Reading");
const ReadingRecord = require("../Model/ReadingRecord");
const ReadingCount = require("../Model/ReadingCount");

const BookHelper = require("../Helper/BookHelper");
const { batchReturnReadingBooks } = require('../src/action/readAction');

const upload = multer();

// 内存缓存
const cache = {};
// 设置缓存过期时间（单位：毫秒）
const CACHE_EXPIRY_TIME = 60000; // 1分钟

app.use("/*", (req, res, next) => next());

app.post("/start", upload.none(), async (req, res) => {
    if (!req.body.book_id || !req.body.client_id) {
        return res.json({
            code: 418,
            message: "Missing required fields",
            status: false,
        });
    }
    const ip = req.header["cf-connecting-ip"] || req.ip;
    const countryCode = req.header("CF-IPCountry") || "Unknown";
    const book_id = req.body.book_id;
    if (countryCode != "HK") {
        return res.json({
            code: 401,
            message: "must be login",
            status: false,
        });
    }
   // 检查是否已经存在相同 client_id + book_id 的未归还阅读记录
    const existingReading = await Reading.findOne({
        client_id: req.body.client_id,
        book_id: book_id,
        returned: false,
    });

    if (existingReading) {
        console.log(`Client ${req.body.client_id} already has active reading for book ${book_id}, reading_id: ${existingReading._id}`);
        res.json({
            code: 200,
            message: "Book start reading",
            data: {
                reading_id: existingReading._id,
            },
        });

        return res.end();
    }
    // NOTE 检查库存
    books_stock = await BookHelper.isBookInStock(book_id);
    if (books_stock <= 0) {
        res.json({
            code: 401,
            message: "Book not has stock",
            status: false,
        });
        return res.end();
    }; 

    const newReading = new Reading({
        book_id: book_id,
        client_id: req.body.client_id,
        ip: ip,
        read_date: new Date(),
        return_date: new Date(),
        returned: false,
    });
    await newReading.save();

    const newReadingRecord = new ReadingRecord({
        reading_id: newReading._id,
        read_time: new Date(),
    });
    await newReadingRecord.save();

    console.log(`New reading started - Client: ${req.body.client_id}, Book: ${book_id}, Reading ID: ${newReading._id}, IP: ${ip}`);
    console.log("record id is:",newReadingRecord._id);

    // NOTE 更新库存前记录当前库存状态
    const bookBefore = await BookHelper.isBookInStock(book_id);
    await BookHelper.updateAvailableStock(book_id, 'reading');
    const bookAfter = await BookHelper.isBookInStock(book_id);
    console.log(`Stock updated for book ${book_id}: ${bookBefore} -> ${bookAfter}`);

    res.json({
        code: 200,
        message: "Book start reading",
        data: {
            reading_id: newReading._id,
        },
    });

    return res.end();

});

app.post("/record", upload.none(), async (req, res) => {
    if (!req.body.reading_id) {
        return res.json({
            code: 422,
            message: "Missing required fields ",
            status: false,
        });
    }
    await Reading.findOne({
        _id: req.body.reading_id,
        returned: false,
    }).then(async (resx) => {
        if (!resx) {
            res.json({
                code: 422,
                message: "No data found",
                status: false,
            });
            return;
        }

        await ReadingRecord.findOneAndUpdate({
            reading_id: mongoose.Types.ObjectId(req.body.reading_id)
        }, {
            read_time: new Date()
        }, {
            upsert: true
        });
        res.json({
            code: 200,
            message: "Reading record created",
            status: true,
        });
    })

    res.end();
});

app.post("/return", upload.none(), async (req, res) => {
    console.log("return Book api",req.body.reading_id);

    if (!req.body.reading_id) {
        return res.json({
            code: 422,
            message: "Missing required fields ",
            status: false,
        });
    }

    // 先获取阅读记录信息
    const reading = await Reading.findOne({
        _id: req.body.reading_id,
    }).populate({
        path: 'book_id',
        select: '_id'
    });

    if (!reading) {
        return res.json({
            code: 422,
            message: "Reading record not found",
            status: false,
        });
    }

    if (reading.returned) {
        console.log(`Reading ${req.body.reading_id} already returned`);
        return res.json({
            code: 200,
            message: "Book already returned",
            status: true,
        });
    }

    const book_id = reading.book_id._id;
    const client_id = reading.client_id;

    console.log(`Returning book - Client: ${client_id}, Book: ${book_id}, Reading ID: ${req.body.reading_id}, IP: ${reading.ip}`);

    // 检查该 client_id 是否还有其他相同书籍的未归还记录
    const otherReadings = await Reading.find({
        client_id: client_id,
        book_id: book_id,
        returned: false,
        _id: { $ne: req.body.reading_id }
    });

    console.log(`Client ${client_id} has ${otherReadings.length} other active readings for book ${book_id}`);

    // 更新当前阅读记录为已归还
    await Reading.updateOne(
        { _id: req.body.reading_id },
        { $set: {
            returned: true,
            return_date: new Date(),
        } }
    );

    // NOTE 记录库存更新前后的状态
    const stockBefore = await BookHelper.isBookInStock(book_id);
    await BookHelper.updateAvailableStock(book_id,'return');
    const stockAfter = await BookHelper.isBookInStock(book_id);
    console.log(`Stock updated for book ${book_id}: ${stockBefore} -> ${stockAfter}`);

    /* Auto Reading the reserved book */
    await syncReturnBooks(book_id);
    console.log("Book is return",req.body.reading_id);
    res.json({
        code: 200,
        message: "Book is return",
        status: true,
    });

    return res.end();
});

app.post("/countIp", upload.none(), async (req, res) => {
    const ip = req.header["cf-connecting-ip"] || req.ip;
    const countryCode = req.header("CF-IPCountry") || "Unknown";
    const newReadingCount = new ReadingCount({
        ip: ip,
        client_id: req.body.client_id || null, // 兼容没有 client_id 的情况
        country_code: countryCode,
        create_at: new Date(),
    });
    await newReadingCount.save();

    res.json({
        code: 200,
        message: "ok",
        status: true,
    });
    return res.end();
});

app.get("/countIp/stat", async (req, res) => {
    try {
        const { startDate, endDate } = req.query;
        let timeFilter = {};
        if (startDate || endDate) {
            timeFilter.create_at = {};
            if (startDate) {
                const ts = parseInt(startDate, 10);
                timeFilter.create_at.$gte = new Date(ts < 1e12 ? ts * 1000 : ts);
              }
        
            if (endDate) {
                const ts = parseInt(endDate, 10);
                timeFilter.create_at.$lte = new Date(ts < 1e12 ? ts * 1000 : ts);
            }
        }
        const pipeline = [
            {
                $facet: {
                    hk: [
                        { $match: { country_code: "HK", ...timeFilter } },
                        {
                            $group: {
                                _id: null,
                                // 优先使用 client_id，如果没有则使用 ip
                                uniqueIdentifiers: {
                                    $addToSet: {
                                        $cond: [
                                            { $and: [{ $ne: ["$client_id", null] }, { $ne: ["$client_id", ""] }] },
                                            { type: "client", id: "$client_id" },
                                            { type: "ip", id: "$ip" }
                                        ]
                                    }
                                },
                                total: { $sum: 1 }
                            }
                        },
                        {
                            $project: {
                                _id: 0,
                                people: { $size: "$uniqueIdentifiers" },
                                times: "$total"
                            }
                        }
                    ],
                    nonHk: [
                        { $match: { country_code: { $ne: "HK" }, ...timeFilter } },
                        {
                            $group: {
                                _id: null,
                                // 优先使用 client_id，如果没有则使用 ip
                                uniqueIdentifiers: {
                                    $addToSet: {
                                        $cond: [
                                            { $and: [{ $ne: ["$client_id", null] }, { $ne: ["$client_id", ""] }] },
                                            { type: "client", id: "$client_id" },
                                            { type: "ip", id: "$ip" }
                                        ]
                                    }
                                },
                                total: { $sum: 1 }
                            }
                        },
                        {
                            $project: {
                                _id: 0,
                                people: { $size: "$uniqueIdentifiers" },
                                times: "$total"
                            }
                        }
                    ]
                }
            }
        ];

        const result = await ReadingCount.aggregate(pipeline);
        const data = result[0];
    
        res.json({
            code: 200,
            message: "ok",
            status: true,
            data: {
                hk: data.hk[0] || { people: 0, times: 0 },
                nonHk: data.nonHk[0] || { people: 0, times: 0 }
            }
        });
        return res.end();
    } catch (error) {
      console.error(error);
      return res.status(500).json({
        code: 500,
        message: "Internal server error",
        status: false
      });
    }
});


module.exports = {
    router: app,
    batchReturnReadingBooks,
};
