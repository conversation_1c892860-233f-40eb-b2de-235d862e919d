const mongoose = require("mongoose");
const Schema = mongoose.Schema;
const Reserve = new Schema({
	user_id: {
        type: Schema.Types.ObjectId,
        ref: "User",
        required: true,
    },
    book_id: {
        type: Schema.Types.ObjectId,
        ref: "Book",
        required: true,
    },
	email: {
		type: String,
		required: true,
	},
	reserve_date: {
		type: Date,
		required: true,
	},
	status: {
		type: Boolean,
		required: true,
	},
	is_deleted: {
		type: Boolean,
		default: false,
	},
	is_mailed:{
		type: Boolean,
		default: false,
	},
	is_ignored:{
		type: Boolean,
		Default: false,
	},
	mail_date: {
		type: Date,
		required: false,
	},
	is_blocked: {
		type: Boolean,
		required: false,
		default: false
	}
});
module.exports = mongoose.model("Reserve", Reserve);